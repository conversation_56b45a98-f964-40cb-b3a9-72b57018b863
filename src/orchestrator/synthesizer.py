"""
Response Synthesizer

This module provides response synthesis and aggregation capabilities for
combining outputs from multiple agents into coherent responses.
"""

import logging
from dataclasses import dataclass
from typing import Any, Dict, List, Optional

from ..agents.base import AgentResponse, AgentType
from ..agents.formatters import MarkdownFormatter
from ..agents.exceptions import AgentResponseError

logger = logging.getLogger(__name__)


@dataclass
class SynthesisResult:
    """Result of response synthesis."""
    content: str
    confidence: float
    sources: List[str]
    metadata: Dict[str, Any]
    synthesis_strategy: str


class ResponseSynthesizer:
    """Synthesizes responses from multiple agents into coherent outputs."""
    
    def __init__(self, formatter: Optional[MarkdownFormatter] = None):
        self.formatter = formatter or MarkdownFormatter()
        
        # Define synthesis strategies
        self.synthesis_strategies = {
            'single_response': self._synthesize_single_response,
            'multi_agent_merge': self._synthesize_multi_agent,
            'hierarchical_synthesis': self._synthesize_hierarchical,
            'fallback_chain': self._synthesize_fallback_chain,
        }
        
        logger.info("Initialized response synthesizer")
    
    def synthesize_responses(
        self, 
        responses: List[AgentResponse],
        synthesis_strategy: str = 'auto',
        query: Optional[str] = None
    ) -> SynthesisResult:
        """Synthesize multiple agent responses into a single coherent response."""
        try:
            if not responses:
                raise AgentResponseError("No responses to synthesize")
            
            # Auto-determine strategy if needed
            if synthesis_strategy == 'auto':
                synthesis_strategy = self._determine_synthesis_strategy(responses)
            
            # Execute synthesis strategy
            if synthesis_strategy not in self.synthesis_strategies:
                raise AgentResponseError(
                    f"Unknown synthesis strategy: {synthesis_strategy}",
                    response_format="synthesis"
                )
            
            synthesis_func = self.synthesis_strategies[synthesis_strategy]
            result = synthesis_func(responses, query)
            
            logger.info(
                f"Synthesized {len(responses)} responses using {synthesis_strategy} strategy"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Response synthesis failed: {e}")
            raise AgentResponseError(
                f"Failed to synthesize responses: {e}",
                response_format="synthesis",
                cause=e
            )
    
    def _determine_synthesis_strategy(self, responses: List[AgentResponse]) -> str:
        """Determine the appropriate synthesis strategy."""
        if len(responses) == 1:
            return 'single_response'
        
        # Check if responses are from different agent types
        agent_types = set(response.agent_type for response in responses)
        
        if len(agent_types) > 1:
            # Multiple different agents
            return 'multi_agent_merge'
        else:
            # Multiple responses from same agent type (fallback chain)
            return 'fallback_chain'
    
    def _synthesize_single_response(
        self, 
        responses: List[AgentResponse], 
        query: Optional[str]
    ) -> SynthesisResult:
        """Handle single response (no synthesis needed)."""
        response = responses[0]
        
        return SynthesisResult(
            content=response.content,
            confidence=response.confidence,
            sources=response.sources,
            metadata=response.metadata,
            synthesis_strategy='single_response'
        )
    
    def _synthesize_multi_agent(
        self, 
        responses: List[AgentResponse], 
        query: Optional[str]
    ) -> SynthesisResult:
        """Synthesize responses from multiple different agents."""
        # Sort responses by agent type priority
        agent_priority = {
            AgentType.TECHNICAL_ARCHITECT: 1,
            AgentType.TASK_PLANNER: 2,
            AgentType.RAG_RETRIEVAL: 3,
            AgentType.ORCHESTRATOR: 4
        }
        
        sorted_responses = sorted(
            responses, 
            key=lambda r: agent_priority.get(r.agent_type, 99)
        )
        
        # Build synthesized content
        content_parts = []
        
        if query:
            content_parts.append(f"# Response to: {query}\n")
        
        for i, response in enumerate(sorted_responses):
            agent_name = response.agent_type.value.replace('_', ' ').title()
            
            # Add agent section header
            content_parts.append(f"## {agent_name} Analysis")
            
            # Add response content
            content_parts.append(response.content)
            
            # Add confidence indicator
            if response.confidence < 0.7:
                content_parts.append(f"*Note: This analysis has moderate confidence ({response.confidence:.1%})*")
            
            content_parts.append("")  # Empty line for spacing
        
        # Combine all sources
        all_sources = []
        for response in responses:
            all_sources.extend(response.sources)
        unique_sources = list(dict.fromkeys(all_sources))  # Remove duplicates, preserve order
        
        # Calculate overall confidence (weighted average)
        total_confidence = sum(r.confidence for r in responses)
        avg_confidence = total_confidence / len(responses) if responses else 0.0
        
        # Combine metadata
        combined_metadata = {
            'agent_count': len(responses),
            'agent_types': [r.agent_type.value for r in responses],
            'individual_confidences': [r.confidence for r in responses],
            'synthesis_method': 'multi_agent_merge'
        }
        
        return SynthesisResult(
            content="\n".join(content_parts).strip(),
            confidence=avg_confidence,
            sources=unique_sources,
            metadata=combined_metadata,
            synthesis_strategy='multi_agent_merge'
        )
    
    def _synthesize_hierarchical(
        self, 
        responses: List[AgentResponse], 
        query: Optional[str]
    ) -> SynthesisResult:
        """Synthesize responses in hierarchical order (architecture -> planning -> implementation)."""
        # Group responses by type
        response_groups = {}
        for response in responses:
            agent_type = response.agent_type
            if agent_type not in response_groups:
                response_groups[agent_type] = []
            response_groups[agent_type].append(response)
        
        # Define hierarchical order
        hierarchy = [
            AgentType.TECHNICAL_ARCHITECT,
            AgentType.TASK_PLANNER,
            AgentType.RAG_RETRIEVAL
        ]
        
        content_parts = []
        if query:
            content_parts.append(f"# Comprehensive Analysis: {query}\n")
        
        all_sources = []
        confidences = []
        
        for agent_type in hierarchy:
            if agent_type in response_groups:
                agent_responses = response_groups[agent_type]
                best_response = max(agent_responses, key=lambda r: r.confidence)
                
                section_title = self._get_section_title(agent_type)
                content_parts.append(f"## {section_title}")
                content_parts.append(best_response.content)
                content_parts.append("")
                
                all_sources.extend(best_response.sources)
                confidences.append(best_response.confidence)
        
        # Handle any remaining agent types not in hierarchy
        remaining_types = set(response_groups.keys()) - set(hierarchy)
        for agent_type in remaining_types:
            agent_responses = response_groups[agent_type]
            best_response = max(agent_responses, key=lambda r: r.confidence)
            
            section_title = self._get_section_title(agent_type)
            content_parts.append(f"## {section_title}")
            content_parts.append(best_response.content)
            content_parts.append("")
            
            all_sources.extend(best_response.sources)
            confidences.append(best_response.confidence)
        
        unique_sources = list(dict.fromkeys(all_sources))
        avg_confidence = sum(confidences) / len(confidences) if confidences else 0.0
        
        return SynthesisResult(
            content="\n".join(content_parts).strip(),
            confidence=avg_confidence,
            sources=unique_sources,
            metadata={
                'synthesis_method': 'hierarchical',
                'hierarchy_order': [t.value for t in hierarchy],
                'response_count': len(responses)
            },
            synthesis_strategy='hierarchical_synthesis'
        )
    
    def _synthesize_fallback_chain(
        self, 
        responses: List[AgentResponse], 
        query: Optional[str]
    ) -> SynthesisResult:
        """Synthesize responses from fallback chain (use best response)."""
        # Sort by confidence and use the best one
        best_response = max(responses, key=lambda r: r.confidence)
        
        # If multiple responses have similar confidence, combine them
        high_confidence_responses = [
            r for r in responses 
            if r.confidence >= best_response.confidence - 0.1
        ]
        
        if len(high_confidence_responses) == 1:
            return self._synthesize_single_response([best_response], query)
        
        # Combine similar-confidence responses
        content_parts = []
        if query:
            content_parts.append(f"# Response to: {query}\n")
        
        content_parts.append("## Primary Response")
        content_parts.append(best_response.content)
        
        if len(high_confidence_responses) > 1:
            content_parts.append("\n## Alternative Perspectives")
            for i, response in enumerate(high_confidence_responses[1:], 1):
                content_parts.append(f"### Alternative {i}")
                content_parts.append(response.content)
        
        all_sources = []
        for response in high_confidence_responses:
            all_sources.extend(response.sources)
        unique_sources = list(dict.fromkeys(all_sources))
        
        return SynthesisResult(
            content="\n".join(content_parts),
            confidence=best_response.confidence,
            sources=unique_sources,
            metadata={
                'synthesis_method': 'fallback_chain',
                'primary_agent': best_response.agent_type.value,
                'alternative_count': len(high_confidence_responses) - 1
            },
            synthesis_strategy='fallback_chain'
        )
    
    def _get_section_title(self, agent_type: AgentType) -> str:
        """Get appropriate section title for agent type."""
        titles = {
            AgentType.TECHNICAL_ARCHITECT: "🏗️ Architecture & Design",
            AgentType.TASK_PLANNER: "📋 Implementation Plan",
            AgentType.RAG_RETRIEVAL: "📚 Code & Documentation",
            AgentType.ORCHESTRATOR: "🎯 Coordination"
        }
        return titles.get(agent_type, agent_type.value.replace('_', ' ').title())
    
    def get_synthesis_stats(self) -> Dict[str, Any]:
        """Get synthesis statistics and capabilities."""
        return {
            'available_strategies': list(self.synthesis_strategies.keys()),
            'default_strategy': 'auto',
            'supported_agent_types': [t.value for t in AgentType]
        }
