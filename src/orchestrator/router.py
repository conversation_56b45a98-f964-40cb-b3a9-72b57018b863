"""
Agent Router

This module provides routing logic to determine which agent(s) should handle
a query, with fallback strategies and multi-agent coordination.
"""

import logging
from dataclasses import dataclass
from typing import Any, Dict, List, Optional, Set

from .classifier import QueryClassifier, ClassificationResult
from ..agents.base import Agent, AgentType, AgentContext
from ..agents.exceptions import AgentRoutingError

logger = logging.getLogger(__name__)


@dataclass
class RoutingDecision:
    """Decision about how to route a query."""
    primary_agent: AgentType
    secondary_agents: List[AgentType]
    routing_strategy: str
    confidence: float
    reasoning: str
    fallback_agents: List[AgentType]


class AgentRouter:
    """Routes queries to appropriate agents with fallback strategies."""
    
    def __init__(self, classifier: Optional[QueryClassifier] = None):
        self.classifier = classifier or QueryClassifier()
        
        # Define routing strategies
        self.routing_strategies = {
            'single_agent': self._route_single_agent,
            'multi_agent': self._route_multi_agent,
            'fallback_chain': self._route_fallback_chain,
        }
        
        # Define agent capabilities and fallback chains
        self.agent_capabilities = {
            AgentType.ORCHESTRATOR: {
                'primary_capabilities': ['coordination', 'synthesis', 'routing'],
                'fallback_for': [],  # Orchestrator doesn't fallback to others
                'can_fallback_to': []
            },
            AgentType.TECHNICAL_ARCHITECT: {
                'primary_capabilities': ['architecture', 'design', 'patterns', 'standards'],
                'fallback_for': [AgentType.TASK_PLANNER],
                'can_fallback_to': [AgentType.RAG_RETRIEVAL]
            },
            AgentType.TASK_PLANNER: {
                'primary_capabilities': ['planning', 'breakdown', 'timeline', 'dependencies'],
                'fallback_for': [AgentType.TECHNICAL_ARCHITECT],
                'can_fallback_to': [AgentType.RAG_RETRIEVAL]
            },
            AgentType.RAG_RETRIEVAL: {
                'primary_capabilities': ['search', 'retrieval', 'factual', 'code_lookup'],
                'fallback_for': [AgentType.TECHNICAL_ARCHITECT, AgentType.TASK_PLANNER],
                'can_fallback_to': []  # RAG is the final fallback
            }
        }
        
        logger.info("Initialized agent router with routing strategies")
    
    def route_query(
        self, 
        query: str, 
        context: AgentContext,
        available_agents: Set[AgentType]
    ) -> RoutingDecision:
        """Route a query to the appropriate agent(s)."""
        try:
            # Classify the query
            classification = self.classifier.classify_query(query)
            
            # Determine routing strategy
            strategy = self._determine_routing_strategy(query, classification, context)
            
            # Execute routing strategy
            routing_func = self.routing_strategies[strategy]
            decision = routing_func(query, classification, context, available_agents)
            
            # Validate routing decision
            self._validate_routing_decision(decision, available_agents)
            
            logger.info(
                f"Routed query to {decision.primary_agent.value} "
                f"(strategy: {strategy}, confidence: {decision.confidence:.2f})"
            )
            
            return decision
            
        except Exception as e:
            logger.error(f"Routing failed: {e}")
            raise AgentRoutingError(
                f"Failed to route query: {e}",
                query_type=getattr(classification, 'intent', 'unknown'),
                available_agents=list(available_agents),
                cause=e
            )
    
    def _determine_routing_strategy(
        self, 
        query: str, 
        classification: ClassificationResult,
        context: AgentContext
    ) -> str:
        """Determine the appropriate routing strategy."""
        # Check if query requires multiple agents
        if self._requires_multi_agent(query, classification):
            return 'multi_agent'
        
        # Check if we need fallback strategy
        if classification.confidence < 0.6:
            return 'fallback_chain'
        
        # Default to single agent
        return 'single_agent'
    
    def _requires_multi_agent(
        self, 
        query: str, 
        classification: ClassificationResult
    ) -> bool:
        """Check if query requires multiple agents."""
        # Complex queries that mention multiple domains
        multi_domain_indicators = [
            ('design', 'implement'),  # Architecture + Planning
            ('plan', 'architecture'),  # Planning + Architecture
            ('create', 'design', 'implement'),  # All three
        ]
        
        query_lower = query.lower()
        for indicators in multi_domain_indicators:
            if all(indicator in query_lower for indicator in indicators):
                return True
        
        # Very long queries might benefit from multi-agent approach
        if len(query.split()) > 25:
            return True
        
        return False
    
    def _route_single_agent(
        self, 
        query: str,
        classification: ClassificationResult,
        context: AgentContext,
        available_agents: Set[AgentType]
    ) -> RoutingDecision:
        """Route to a single primary agent."""
        primary_agent = classification.agent_type
        
        # Check if primary agent is available
        if primary_agent not in available_agents:
            # Find fallback
            fallback_agents = self._get_fallback_agents(primary_agent, available_agents)
            if fallback_agents:
                primary_agent = fallback_agents[0]
            else:
                raise AgentRoutingError(
                    f"Primary agent {primary_agent.value} not available and no fallbacks found",
                    query_type=classification.intent.value,
                    available_agents=list(available_agents)
                )
        
        return RoutingDecision(
            primary_agent=primary_agent,
            secondary_agents=[],
            routing_strategy='single_agent',
            confidence=classification.confidence,
            reasoning=f"Single agent routing: {classification.reasoning}",
            fallback_agents=self._get_fallback_agents(primary_agent, available_agents)
        )
    
    def _route_multi_agent(
        self, 
        query: str,
        classification: ClassificationResult,
        context: AgentContext,
        available_agents: Set[AgentType]
    ) -> RoutingDecision:
        """Route to multiple agents for complex queries."""
        primary_agent = classification.agent_type
        secondary_agents = []
        
        # Determine secondary agents based on query content
        query_lower = query.lower()
        
        if 'design' in query_lower and 'implement' in query_lower:
            # Architecture + Planning
            if primary_agent == AgentType.TECHNICAL_ARCHITECT:
                secondary_agents.append(AgentType.TASK_PLANNER)
            elif primary_agent == AgentType.TASK_PLANNER:
                secondary_agents.append(AgentType.TECHNICAL_ARCHITECT)
        
        # Filter available agents
        secondary_agents = [agent for agent in secondary_agents if agent in available_agents]
        
        return RoutingDecision(
            primary_agent=primary_agent,
            secondary_agents=secondary_agents,
            routing_strategy='multi_agent',
            confidence=classification.confidence * 0.9,  # Slightly lower for complexity
            reasoning=f"Multi-agent routing: {classification.reasoning} + secondary agents",
            fallback_agents=self._get_fallback_agents(primary_agent, available_agents)
        )
    
    def _route_fallback_chain(
        self, 
        query: str,
        classification: ClassificationResult,
        context: AgentContext,
        available_agents: Set[AgentType]
    ) -> RoutingDecision:
        """Route with fallback chain for uncertain classifications."""
        primary_agent = classification.agent_type
        fallback_agents = self._get_fallback_agents(primary_agent, available_agents)
        
        # If confidence is very low, start with RAG retrieval
        if classification.confidence < 0.4:
            primary_agent = AgentType.RAG_RETRIEVAL
            fallback_agents = [classification.agent_type] + fallback_agents
        
        return RoutingDecision(
            primary_agent=primary_agent,
            secondary_agents=[],
            routing_strategy='fallback_chain',
            confidence=max(classification.confidence, 0.5),  # Boost confidence with fallback
            reasoning=f"Fallback routing: {classification.reasoning} with fallback chain",
            fallback_agents=fallback_agents
        )
    
    def _get_fallback_agents(
        self, 
        primary_agent: AgentType, 
        available_agents: Set[AgentType]
    ) -> List[AgentType]:
        """Get fallback agents for a primary agent."""
        if primary_agent not in self.agent_capabilities:
            return []
        
        fallback_candidates = self.agent_capabilities[primary_agent]['can_fallback_to']
        return [agent for agent in fallback_candidates if agent in available_agents]
    
    def _validate_routing_decision(
        self, 
        decision: RoutingDecision, 
        available_agents: Set[AgentType]
    ) -> None:
        """Validate that routing decision is feasible."""
        # Check primary agent availability
        if decision.primary_agent not in available_agents:
            raise AgentRoutingError(
                f"Primary agent {decision.primary_agent.value} not available",
                available_agents=list(available_agents)
            )
        
        # Check secondary agents availability
        for agent in decision.secondary_agents:
            if agent not in available_agents:
                raise AgentRoutingError(
                    f"Secondary agent {agent.value} not available",
                    available_agents=list(available_agents)
                )
        
        # Check confidence bounds
        if not 0.0 <= decision.confidence <= 1.0:
            raise AgentRoutingError(
                f"Invalid confidence score: {decision.confidence}",
                routing_confidence=decision.confidence
            )
    
    def get_routing_stats(self) -> Dict[str, Any]:
        """Get routing statistics and capabilities."""
        return {
            'available_strategies': list(self.routing_strategies.keys()),
            'agent_capabilities': {
                agent.value: capabilities 
                for agent, capabilities in self.agent_capabilities.items()
            },
            'classification_stats': self.classifier.get_classification_stats()
        }
