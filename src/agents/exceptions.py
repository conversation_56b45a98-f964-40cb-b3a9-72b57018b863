"""
Agent System Exceptions

This module defines custom exceptions for the agent system with detailed error context
and chaining for better debugging and monitoring.
"""

from typing import Any, Dict, Optional


class AgentError(Exception):
    """Base exception for all agent-related errors."""
    
    def __init__(
        self,
        message: str,
        agent_type: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        super().__init__(message)
        self.agent_type = agent_type
        self.details = details or {}
        self.cause = cause
    
    def __str__(self) -> str:
        """Return detailed error message."""
        parts = [super().__str__()]
        
        if self.agent_type:
            parts.append(f"Agent: {self.agent_type}")
        
        if self.details:
            details_str = ", ".join(f"{k}={v}" for k, v in self.details.items())
            parts.append(f"Details: {details_str}")
        
        if self.cause:
            parts.append(f"Caused by: {self.cause}")
        
        return " | ".join(parts)


class AgentTimeoutError(AgentError):
    """Raised when an agent operation times out."""
    
    def __init__(
        self,
        message: str = "Agent operation timed out",
        agent_type: Optional[str] = None,
        timeout_seconds: Optional[float] = None,
        operation: Optional[str] = None
    ):
        details = {}
        if timeout_seconds is not None:
            details["timeout_seconds"] = timeout_seconds
        if operation:
            details["operation"] = operation
        
        super().__init__(message, agent_type, details)


class AgentConfigurationError(AgentError):
    """Raised when there's an agent configuration issue."""
    
    def __init__(
        self,
        message: str = "Agent configuration error",
        agent_type: Optional[str] = None,
        config_key: Optional[str] = None,
        expected_type: Optional[str] = None
    ):
        details = {}
        if config_key:
            details["config_key"] = config_key
        if expected_type:
            details["expected_type"] = expected_type
        
        super().__init__(message, agent_type, details)


class LLMClientError(AgentError):
    """Raised when there's an LLM client error."""
    
    def __init__(
        self,
        message: str = "LLM client error",
        model: Optional[str] = None,
        status_code: Optional[int] = None,
        retry_count: Optional[int] = None,
        cause: Optional[Exception] = None
    ):
        details = {}
        if model:
            details["model"] = model
        if status_code is not None:
            details["status_code"] = status_code
        if retry_count is not None:
            details["retry_count"] = retry_count
        
        super().__init__(message, "llm_client", details, cause)


class ContextError(AgentError):
    """Raised when there's a conversation context error."""
    
    def __init__(
        self,
        message: str = "Context management error",
        session_id: Optional[str] = None,
        context_size: Optional[int] = None,
        operation: Optional[str] = None
    ):
        details = {}
        if session_id:
            details["session_id"] = session_id
        if context_size is not None:
            details["context_size"] = context_size
        if operation:
            details["operation"] = operation
        
        super().__init__(message, "context_manager", details)


class AgentRoutingError(AgentError):
    """Raised when there's an error in agent routing."""
    
    def __init__(
        self,
        message: str = "Agent routing error",
        query_type: Optional[str] = None,
        available_agents: Optional[list] = None,
        routing_confidence: Optional[float] = None
    ):
        details = {}
        if query_type:
            details["query_type"] = query_type
        if available_agents:
            details["available_agents"] = available_agents
        if routing_confidence is not None:
            details["routing_confidence"] = routing_confidence
        
        super().__init__(message, "orchestrator", details)


class AgentResponseError(AgentError):
    """Raised when there's an error in agent response processing."""
    
    def __init__(
        self,
        message: str = "Agent response error",
        agent_type: Optional[str] = None,
        response_format: Optional[str] = None,
        validation_errors: Optional[list] = None
    ):
        details = {}
        if response_format:
            details["response_format"] = response_format
        if validation_errors:
            details["validation_errors"] = validation_errors
        
        super().__init__(message, agent_type, details)
