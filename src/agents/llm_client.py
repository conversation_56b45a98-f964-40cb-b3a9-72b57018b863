"""
LLM Client for Agent System

This module provides a unified interface for LLM interactions across all agents,
with consistent error handling, rate limiting, and response formatting.
"""

import asyncio
import logging
import time
from dataclasses import dataclass
from typing import Any, Dict, List, Optional
from openai import AsyncOpenAI

from .exceptions import LLMClientError
from ..config import Settings

logger = logging.getLogger(__name__)


@dataclass
class LLMResponse:
    """Response from LLM client."""
    content: str
    model: str
    usage: Dict[str, Any]
    processing_time: float
    metadata: Dict[str, Any]
    
    @property
    def token_count(self) -> int:
        """Get total token count from usage."""
        return self.usage.get("total_tokens", 0)
    
    @property
    def cost_estimate(self) -> float:
        """Estimate cost based on token usage (rough estimate)."""
        # Rough cost estimates for GPT-4 (adjust based on actual model)
        input_tokens = self.usage.get("prompt_tokens", 0)
        output_tokens = self.usage.get("completion_tokens", 0)
        
        # GPT-4 pricing (approximate)
        input_cost = input_tokens * 0.00003  # $0.03 per 1K tokens
        output_cost = output_tokens * 0.00006  # $0.06 per 1K tokens
        
        return input_cost + output_cost


class LLMClient:
    """Unified LLM client for agent interactions."""
    
    def __init__(self, settings: Settings):
        """Initialize LLM client."""
        self.settings = settings
        self.client = AsyncOpenAI(api_key=settings.openai_api_key)
        self.model = settings.openai_model
        self.temperature = settings.openai_temperature
        self.max_tokens = settings.openai_max_tokens
        
        # Rate limiting
        self._last_request_time = 0.0
        self._min_request_interval = 0.1  # 100ms between requests
        
        # Statistics
        self._stats = {
            "total_requests": 0,
            "total_tokens": 0,
            "total_cost": 0.0,
            "failed_requests": 0,
            "average_response_time": 0.0,
        }
        
        logger.info(f"Initialized LLM client with model {self.model}")
    
    async def generate_response(
        self,
        prompt: str,
        system_prompt: Optional[str] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        context: Optional[Dict[str, Any]] = None
    ) -> LLMResponse:
        """Generate response from LLM."""
        start_time = time.time()
        
        try:
            # Rate limiting
            await self._enforce_rate_limit()
            
            # Prepare messages
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})
            
            # Use provided parameters or defaults
            temp = temperature if temperature is not None else self.temperature
            max_tok = max_tokens if max_tokens is not None else self.max_tokens
            
            # Make API call
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=messages,
                temperature=temp,
                max_tokens=max_tok,
                timeout=60.0
            )
            
            processing_time = time.time() - start_time
            
            # Extract response content
            content = response.choices[0].message.content or ""
            
            # Create response object
            llm_response = LLMResponse(
                content=content,
                model=self.model,
                usage=response.usage.model_dump() if response.usage else {},
                processing_time=processing_time,
                metadata={
                    "temperature": temp,
                    "max_tokens": max_tok,
                    "context": context or {},
                }
            )
            
            # Update statistics
            self._update_stats(llm_response, success=True)
            
            logger.debug(
                f"LLM response generated in {processing_time:.2f}s, "
                f"{llm_response.token_count} tokens"
            )
            
            return llm_response
            
        except Exception as e:
            processing_time = time.time() - start_time
            self._update_stats(None, success=False)
            
            logger.error(f"LLM client error: {e}")
            raise LLMClientError(
                f"Failed to generate LLM response: {e}",
                model=self.model,
                cause=e
            )
    
    async def _enforce_rate_limit(self) -> None:
        """Enforce rate limiting between requests."""
        current_time = time.time()
        time_since_last = current_time - self._last_request_time
        
        if time_since_last < self._min_request_interval:
            sleep_time = self._min_request_interval - time_since_last
            await asyncio.sleep(sleep_time)
        
        self._last_request_time = time.time()
    
    def _update_stats(self, response: Optional[LLMResponse], success: bool) -> None:
        """Update client statistics."""
        self._stats["total_requests"] += 1
        
        if success and response:
            self._stats["total_tokens"] += response.token_count
            self._stats["total_cost"] += response.cost_estimate
            
            # Update average response time
            total_time = (
                self._stats["average_response_time"] * (self._stats["total_requests"] - 1) +
                response.processing_time
            )
            self._stats["average_response_time"] = total_time / self._stats["total_requests"]
        else:
            self._stats["failed_requests"] += 1
    
    def get_stats(self) -> Dict[str, Any]:
        """Get client statistics."""
        return self._stats.copy()
    
    def reset_stats(self) -> None:
        """Reset client statistics."""
        self._stats = {
            "total_requests": 0,
            "total_tokens": 0,
            "total_cost": 0.0,
            "failed_requests": 0,
            "average_response_time": 0.0,
        }


class LLMClientFactory:
    """Factory for creating LLM clients."""
    
    @staticmethod
    def create_client(settings: Settings) -> LLMClient:
        """Create an LLM client based on settings."""
        # For now, only OpenAI is supported
        # Future: Add support for other providers (Anthropic, Cohere, etc.)
        return LLMClient(settings)
    
    @staticmethod
    def get_available_providers() -> List[str]:
        """Get list of available LLM providers."""
        return ["openai"]  # Add more as they're implemented
