"""
Agents Module

This module provides the multi-agent orchestration system for the LLM RAG Codebase Query System.
It includes specialized agents for different types of queries and a central orchestrator.
"""

from .exceptions import (
    AgentError,
    AgentTimeoutError,
    AgentConfigurationError,
    LLMClientError,
    ContextError,
)

from .base import (
    Agent,
    AgentType,
    AgentResponse,
    AgentContext,
    ConversationContext,
    Message,
    MessageRole,
)

from .context import (
    ConversationContextManager,
    ContextStorage,
    InMemoryContextStorage,
)

from .formatters import (
    ResponseFormatter,
    MarkdownFormatter,
    SourceCitationFormatter,
)

from .llm_client import (
    LLMClient,
    LLMResponse,
    LLMClientFactory,
)

from .factory import (
    AgentFactory,
    AgentRegistry,
)

from .rag_agent import RAGRetrievalAgent

__all__ = [
    # Exceptions
    "AgentError",
    "AgentTimeoutError", 
    "AgentConfigurationError",
    "LLMClientError",
    "ContextError",
    # Base classes
    "Agent",
    "AgentType",
    "AgentResponse",
    "AgentContext",
    "ConversationContext",
    "Message",
    "MessageRole",
    # Context management
    "ConversationContextManager",
    "ContextStorage",
    "InMemoryContextStorage",
    # Formatting
    "ResponseFormatter",
    "MarkdownFormatter",
    "SourceCitationFormatter",
    # LLM integration
    "LLMClient",
    "LLMResponse",
    "LLMClientFactory",
    # Factory and registry
    "AgentFactory",
    "AgentRegistry",
    # Specific agents
    "RAGRetrievalAgent",
]
