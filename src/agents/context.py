"""
Conversation Context Management

This module provides context storage and management for multi-turn conversations,
supporting both in-memory and persistent storage backends.
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from uuid import uuid4

from .base import ConversationContext, Message
from .exceptions import ContextError

logger = logging.getLogger(__name__)


class ContextStorage(ABC):
    """Abstract base class for context storage backends."""
    
    @abstractmethod
    async def store_context(self, context: ConversationContext) -> bool:
        """Store conversation context."""
        pass
    
    @abstractmethod
    async def retrieve_context(self, session_id: str) -> Optional[ConversationContext]:
        """Retrieve conversation context by session ID."""
        pass
    
    @abstractmethod
    async def delete_context(self, session_id: str) -> bool:
        """Delete conversation context."""
        pass
    
    @abstractmethod
    async def list_active_sessions(self, user_id: Optional[str] = None) -> List[str]:
        """List active session IDs."""
        pass
    
    @abstractmethod
    async def cleanup_expired_sessions(self, max_age_hours: int = 24) -> int:
        """Clean up expired sessions and return count of deleted sessions."""
        pass


class InMemoryContextStorage(ContextStorage):
    """In-memory context storage for development and testing."""
    
    def __init__(self):
        self._contexts: Dict[str, ConversationContext] = {}
        self._lock = asyncio.Lock()
        logger.info("Initialized in-memory context storage")
    
    async def store_context(self, context: ConversationContext) -> bool:
        """Store conversation context in memory."""
        try:
            async with self._lock:
                context.last_updated = datetime.now()
                self._contexts[context.session_id] = context
                logger.debug(f"Stored context for session {context.session_id}")
                return True
        except Exception as e:
            logger.error(f"Failed to store context: {e}")
            return False
    
    async def retrieve_context(self, session_id: str) -> Optional[ConversationContext]:
        """Retrieve conversation context from memory."""
        try:
            async with self._lock:
                context = self._contexts.get(session_id)
                if context:
                    logger.debug(f"Retrieved context for session {session_id}")
                return context
        except Exception as e:
            logger.error(f"Failed to retrieve context: {e}")
            return None
    
    async def delete_context(self, session_id: str) -> bool:
        """Delete conversation context from memory."""
        try:
            async with self._lock:
                if session_id in self._contexts:
                    del self._contexts[session_id]
                    logger.debug(f"Deleted context for session {session_id}")
                    return True
                return False
        except Exception as e:
            logger.error(f"Failed to delete context: {e}")
            return False
    
    async def list_active_sessions(self, user_id: Optional[str] = None) -> List[str]:
        """List active session IDs."""
        try:
            async with self._lock:
                if user_id:
                    return [
                        session_id for session_id, context in self._contexts.items()
                        if context.user_id == user_id
                    ]
                return list(self._contexts.keys())
        except Exception as e:
            logger.error(f"Failed to list sessions: {e}")
            return []
    
    async def cleanup_expired_sessions(self, max_age_hours: int = 24) -> int:
        """Clean up expired sessions."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=max_age_hours)
            expired_sessions = []
            
            async with self._lock:
                for session_id, context in self._contexts.items():
                    if context.last_updated < cutoff_time:
                        expired_sessions.append(session_id)
                
                for session_id in expired_sessions:
                    del self._contexts[session_id]
            
            logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
            return len(expired_sessions)
        except Exception as e:
            logger.error(f"Failed to cleanup sessions: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """Get storage statistics."""
        return {
            "total_sessions": len(self._contexts),
            "storage_type": "in_memory",
        }


class ConversationContextManager:
    """Manages conversation contexts with automatic cleanup and validation."""
    
    def __init__(self, storage: ContextStorage, max_context_size: int = 50):
        self.storage = storage
        self.max_context_size = max_context_size
        self._cleanup_task: Optional[asyncio.Task] = None
        logger.info("Initialized conversation context manager")
    
    async def create_session(
        self, 
        user_id: Optional[str] = None,
        repository: Optional[str] = None
    ) -> ConversationContext:
        """Create a new conversation session."""
        session_id = str(uuid4())
        context = ConversationContext(
            session_id=session_id,
            user_id=user_id,
            current_repository=repository
        )
        
        success = await self.storage.store_context(context)
        if not success:
            raise ContextError(
                "Failed to create conversation session",
                session_id=session_id,
                operation="create"
            )
        
        logger.info(f"Created new session {session_id} for user {user_id}")
        return context
    
    async def get_context(self, session_id: str) -> Optional[ConversationContext]:
        """Get conversation context by session ID."""
        context = await self.storage.retrieve_context(session_id)
        if context:
            logger.debug(f"Retrieved context for session {session_id}")
        return context
    
    async def add_message(
        self, 
        session_id: str, 
        message: Message
    ) -> ConversationContext:
        """Add a message to the conversation."""
        context = await self.get_context(session_id)
        if not context:
            raise ContextError(
                "Session not found",
                session_id=session_id,
                operation="add_message"
            )
        
        # Add message and manage context size
        context.add_message(message)
        
        # Trim context if it exceeds max size
        if len(context.conversation_history) > self.max_context_size:
            # Keep the most recent messages
            context.conversation_history = context.conversation_history[-self.max_context_size:]
            logger.debug(f"Trimmed context for session {session_id} to {self.max_context_size} messages")
        
        # Store updated context
        success = await self.storage.store_context(context)
        if not success:
            raise ContextError(
                "Failed to update conversation context",
                session_id=session_id,
                operation="add_message"
            )
        
        return context
    
    async def update_repository(
        self, 
        session_id: str, 
        repository: str
    ) -> ConversationContext:
        """Update the current repository for a session."""
        context = await self.get_context(session_id)
        if not context:
            raise ContextError(
                "Session not found",
                session_id=session_id,
                operation="update_repository"
            )
        
        context.current_repository = repository
        context.last_updated = datetime.now()
        
        success = await self.storage.store_context(context)
        if not success:
            raise ContextError(
                "Failed to update repository context",
                session_id=session_id,
                operation="update_repository"
            )
        
        logger.info(f"Updated repository for session {session_id} to {repository}")
        return context
    
    async def delete_session(self, session_id: str) -> bool:
        """Delete a conversation session."""
        success = await self.storage.delete_context(session_id)
        if success:
            logger.info(f"Deleted session {session_id}")
        return success
    
    async def start_cleanup_task(self, cleanup_interval_hours: int = 6) -> None:
        """Start automatic cleanup of expired sessions."""
        if self._cleanup_task and not self._cleanup_task.done():
            logger.warning("Cleanup task already running")
            return
        
        async def cleanup_loop():
            while True:
                try:
                    await asyncio.sleep(cleanup_interval_hours * 3600)  # Convert to seconds
                    deleted_count = await self.storage.cleanup_expired_sessions()
                    logger.info(f"Automatic cleanup removed {deleted_count} expired sessions")
                except asyncio.CancelledError:
                    logger.info("Cleanup task cancelled")
                    break
                except Exception as e:
                    logger.error(f"Error in cleanup task: {e}")
        
        self._cleanup_task = asyncio.create_task(cleanup_loop())
        logger.info(f"Started automatic cleanup task (interval: {cleanup_interval_hours}h)")
    
    async def stop_cleanup_task(self) -> None:
        """Stop the automatic cleanup task."""
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            logger.info("Stopped automatic cleanup task")
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get context manager statistics."""
        storage_stats = {}
        if hasattr(self.storage, 'get_stats'):
            storage_stats = self.storage.get_stats()
        
        return {
            "max_context_size": self.max_context_size,
            "cleanup_task_running": self._cleanup_task and not self._cleanup_task.done(),
            "storage": storage_stats,
        }
