"""
Integration Example

This module demonstrates how to integrate the chunking pipeline with the
embedding pipeline for end-to-end processing from repository to vector store.
"""

import asyncio
import logging
from pathlib import Path
from typing import List, Dict, Any

from .github import GitHubConnector
from .pipeline import DefaultChunkingPipeline
from .embedding_pipeline import DefaultEmbeddingPipeline
from ..config import get_settings

logger = logging.getLogger(__name__)


class IntegratedIngestionPipeline:
    """Integrated pipeline combining GitHub ingestion, chunking, and embedding."""
    
    def __init__(self, settings=None):
        """Initialize the integrated pipeline."""
        self.settings = settings or get_settings()
        
        # Initialize components
        self.github_connector = GitHubConnector(self.settings)
        self.chunking_pipeline = DefaultChunkingPipeline(self.settings)
        self.embedding_pipeline = DefaultEmbeddingPipeline(self.settings)
        
        logger.info("Initialized integrated ingestion pipeline")
    
    async def process_repository(
        self, 
        repository_url: str, 
        branch: str = "main"
    ) -> Dict[str, Any]:
        """Process a complete repository from GitHub to vector store."""
        logger.info(f"Starting integrated processing for repository: {repository_url}")
        
        try:
            # Step 1: Ingest repository with GitHub Connector
            logger.info("Step 1: Ingesting repository from GitHub...")
            github_result = await self.github_connector.ingest_repository(
                repository_url, branch
            )
            
            file_metadata_list = github_result["file_metadata"]
            repository_path = Path(github_result["local_path"])
            
            logger.info(f"Ingested {len(file_metadata_list)} files from repository")
            
            # Step 2: Generate chunks with Chunking Pipeline
            logger.info("Step 2: Generating semantic chunks...")
            chunks = await self.chunking_pipeline.process_files(
                file_metadata_list, repository_path
            )
            
            logger.info(f"Generated {len(chunks)} chunks from repository content")
            
            # Step 3: Generate embeddings with Embedding Pipeline
            logger.info("Step 3: Generating embeddings...")
            embedded_chunks = await self.embedding_pipeline.process_chunks(chunks)
            
            logger.info(f"Generated embeddings for {len(embedded_chunks)} chunks")
            
            # Step 4: Store embeddings in vector store
            logger.info("Step 4: Storing embeddings in vector store...")
            storage_success = await self.embedding_pipeline.store_embeddings(embedded_chunks)
            
            if storage_success:
                logger.info("Successfully stored embeddings in vector store")
            else:
                logger.warning("Failed to store some embeddings")
            
            # Compile results
            result = {
                "repository_url": repository_url,
                "branch": branch,
                "repository_info": github_result["repository_info"],
                "files_processed": len(file_metadata_list),
                "chunks_generated": len(chunks),
                "embeddings_created": len(embedded_chunks),
                "storage_success": storage_success,
                "local_path": str(repository_path),
                "processing_stats": {
                    "github_stats": github_result.get("stats", {}),
                    "chunking_stats": self.chunking_pipeline.get_processing_stats(),
                    "embedding_stats": await self.embedding_pipeline.get_pipeline_stats(),
                }
            }
            
            logger.info(
                f"Integrated processing completed successfully: "
                f"{result['files_processed']} files → "
                f"{result['chunks_generated']} chunks → "
                f"{result['embeddings_created']} embeddings"
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Integrated processing failed: {e}")
            raise
    
    async def search_repository(
        self, 
        query: str, 
        top_k: int = 10,
        filters: Dict[str, Any] = None
    ) -> List[Dict[str, Any]]:
        """Search the processed repository using semantic similarity."""
        logger.info(f"Searching repository with query: '{query}'")
        
        try:
            # Search using embedding pipeline
            search_results = await self.embedding_pipeline.search_similar_chunks(
                query=query,
                top_k=top_k,
                filters=filters
            )
            
            # Convert results to dictionary format
            results = []
            for result in search_results:
                result_dict = {
                    "content": result.content,
                    "similarity_score": result.similarity_score,
                    "rank": result.rank,
                    "file_path": result.chunk.file_metadata.file_path,
                    "chunk_type": result.chunk.chunk_type.value,
                    "line_range": result.chunk.line_range,
                    "context": result.chunk.full_context_path,
                    "language": result.chunk.file_metadata.language,
                }
                results.append(result_dict)
            
            logger.info(f"Found {len(results)} relevant chunks for query")
            return results
            
        except Exception as e:
            logger.error(f"Search failed: {e}")
            raise
    
    async def get_comprehensive_stats(self) -> Dict[str, Any]:
        """Get comprehensive statistics from all pipeline components."""
        try:
            stats = {
                "chunking_pipeline": self.chunking_pipeline.get_processing_stats(),
                "embedding_pipeline": await self.embedding_pipeline.get_pipeline_stats(),
            }
            
            # Add vector store collection info
            try:
                collection_info = await self.embedding_pipeline.vector_store.get_collection_info()
                stats["vector_store"] = collection_info
            except Exception as e:
                logger.debug(f"Could not get vector store stats: {e}")
            
            return stats
            
        except Exception as e:
            logger.error(f"Failed to get comprehensive stats: {e}")
            return {}
    
    def reset_all_stats(self) -> None:
        """Reset statistics for all pipeline components."""
        self.chunking_pipeline.reset_stats()
        self.embedding_pipeline.reset_stats()
        logger.info("Reset statistics for all pipeline components")


async def example_usage():
    """Example usage of the integrated pipeline."""
    # Initialize pipeline
    pipeline = IntegratedIngestionPipeline()
    
    # Process a repository
    repository_url = "https://github.com/user/example-repo"
    result = await pipeline.process_repository(repository_url)
    
    print(f"Processed repository: {result['repository_url']}")
    print(f"Files: {result['files_processed']}")
    print(f"Chunks: {result['chunks_generated']}")
    print(f"Embeddings: {result['embeddings_created']}")
    
    # Search the repository
    search_results = await pipeline.search_repository(
        query="How to implement authentication?",
        top_k=5
    )
    
    print(f"\nSearch results for authentication query:")
    for i, result in enumerate(search_results, 1):
        print(f"{i}. {result['file_path']} (Score: {result['similarity_score']:.3f})")
        print(f"   Context: {result['context']}")
        print(f"   Content: {result['content'][:100]}...")
        print()
    
    # Get comprehensive stats
    stats = await pipeline.get_comprehensive_stats()
    print(f"\nPipeline Statistics:")
    print(f"Chunking: {stats.get('chunking_pipeline', {})}")
    print(f"Embedding: {stats.get('embedding_pipeline', {})}")


if __name__ == "__main__":
    # Run example
    asyncio.run(example_usage())
