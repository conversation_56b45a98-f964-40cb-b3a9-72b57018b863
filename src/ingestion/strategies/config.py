"""
Configuration File Chunking Strategy

This module provides chunking strategy for configuration files including
JSON, YAML, TOML, INI, and other structured data formats.
"""

import logging
import json
import re
from typing import List, Dict, Any, Optional
import yaml

from ..base import Chunk, ChunkType, ChunkContext, FileMetadata
from .base import BaseChunkingStrategy

logger = logging.getLogger(__name__)


class ConfigChunkingStrategy(BaseChunkingStrategy):
    """Chunking strategy for configuration files."""
    
    def get_strategy_name(self) -> str:
        """Get the name of this chunking strategy."""
        return "config"
    
    def is_applicable(self, file_metadata: FileMetadata) -> bool:
        """Check if this strategy is applicable to the file."""
        file_extension = file_metadata.file_path.split('.')[-1].lower() if '.' in file_metadata.file_path else ''
        config_extensions = {
            'json', 'yaml', 'yml', 'toml', 'ini', 'cfg', 'conf', 
            'env', 'properties', 'plist'
        }
        return file_extension in config_extensions
    
    async def chunk_content(
        self, 
        content: str, 
        file_metadata: FileMetadata,
        **kwargs
    ) -> List[Chunk]:
        """Chunk configuration content based on structure."""
        try:
            if not content.strip():
                return []
            
            file_extension = file_metadata.file_path.split('.')[-1].lower() if '.' in file_metadata.file_path else ''
            
            # Choose chunking method based on file type
            if file_extension == 'json':
                chunks = self._chunk_json(content, file_metadata)
            elif file_extension in ['yaml', 'yml']:
                chunks = self._chunk_yaml(content, file_metadata)
            elif file_extension == 'toml':
                chunks = self._chunk_toml(content, file_metadata)
            elif file_extension in ['ini', 'cfg', 'conf']:
                chunks = self._chunk_ini(content, file_metadata)
            elif file_extension in ['env', 'properties']:
                chunks = self._chunk_properties(content, file_metadata)
            else:
                # Fallback to generic key-value chunking
                chunks = self._chunk_generic(content, file_metadata)
            
            logger.debug(f"Created {len(chunks)} config chunks for {file_metadata.file_path}")
            return chunks
            
        except Exception as e:
            logger.error(f"Failed to chunk config content for {file_metadata.file_path}: {e}")
            # Fallback to generic chunking
            return self._chunk_generic(content, file_metadata)
    
    def _chunk_json(self, content: str, file_metadata: FileMetadata) -> List[Chunk]:
        """Chunk JSON configuration files."""
        try:
            data = json.loads(content)
            return self._chunk_structured_data(data, content, file_metadata, 'json')
        except json.JSONDecodeError as e:
            logger.warning(f"Invalid JSON in {file_metadata.file_path}: {e}")
            return self._chunk_generic(content, file_metadata)
    
    def _chunk_yaml(self, content: str, file_metadata: FileMetadata) -> List[Chunk]:
        """Chunk YAML configuration files."""
        try:
            data = yaml.safe_load(content)
            return self._chunk_structured_data(data, content, file_metadata, 'yaml')
        except yaml.YAMLError as e:
            logger.warning(f"Invalid YAML in {file_metadata.file_path}: {e}")
            return self._chunk_generic(content, file_metadata)
    
    def _chunk_toml(self, content: str, file_metadata: FileMetadata) -> List[Chunk]:
        """Chunk TOML configuration files."""
        try:
            # Basic TOML parsing using regex (can be enhanced with toml library)
            sections = self._parse_toml_sections(content)
            return self._create_section_chunks(sections, file_metadata, 'toml')
        except Exception as e:
            logger.warning(f"Failed to parse TOML in {file_metadata.file_path}: {e}")
            return self._chunk_generic(content, file_metadata)
    
    def _chunk_ini(self, content: str, file_metadata: FileMetadata) -> List[Chunk]:
        """Chunk INI/CFG configuration files."""
        sections = self._parse_ini_sections(content)
        return self._create_section_chunks(sections, file_metadata, 'ini')
    
    def _chunk_properties(self, content: str, file_metadata: FileMetadata) -> List[Chunk]:
        """Chunk properties/env files."""
        # Group related properties by prefix
        groups = self._group_properties_by_prefix(content)
        return self._create_property_chunks(groups, file_metadata)
    
    def _chunk_structured_data(self, data: Any, content: str, file_metadata: FileMetadata, format_type: str) -> List[Chunk]:
        """Chunk structured data (JSON/YAML)."""
        chunks = []
        
        if isinstance(data, dict):
            # Chunk by top-level keys
            lines = content.split('\n')
            current_line = 1
            
            for i, (key, value) in enumerate(data.items()):
                # Find the section in the original content
                section_content = self._extract_section_content(key, value, content, format_type)
                
                # Estimate line numbers (approximate)
                section_lines = section_content.count('\n')
                start_line = current_line
                end_line = current_line + section_lines
                
                context = ChunkContext(
                    section_headers=[key],
                    namespace=key
                )
                
                chunk = self.create_chunk(
                    content=section_content,
                    file_metadata=file_metadata,
                    start_line=start_line,
                    end_line=end_line,
                    chunk_type=ChunkType.CONFIG_SECTION,
                    chunk_index=i,
                    context=context
                )
                
                chunks.append(chunk)
                current_line = end_line + 1
        
        else:
            # Single chunk for non-dict data
            chunk = self.create_chunk(
                content=content,
                file_metadata=file_metadata,
                start_line=1,
                end_line=len(content.split('\n')),
                chunk_type=ChunkType.CONFIG_SECTION,
                chunk_index=0,
                context=ChunkContext()
            )
            chunks.append(chunk)
        
        return chunks
    
    def _extract_section_content(self, key: str, value: Any, content: str, format_type: str) -> str:
        """Extract section content from original text."""
        if format_type == 'json':
            # For JSON, try to extract the key-value pair
            try:
                key_pattern = rf'"{re.escape(key)}"\s*:\s*'
                match = re.search(key_pattern, content)
                if match:
                    start = match.start()
                    # Find the end of this key's value (simplified)
                    if isinstance(value, (dict, list)):
                        # For complex values, include the whole structure
                        return json.dumps({key: value}, indent=2)
                    else:
                        # For simple values, extract the line
                        line_start = content.rfind('\n', 0, start) + 1
                        line_end = content.find('\n', match.end())
                        if line_end == -1:
                            line_end = len(content)
                        return content[line_start:line_end].strip()
            except:
                pass
        
        # Fallback: serialize the value
        if format_type == 'json':
            return json.dumps({key: value}, indent=2)
        elif format_type == 'yaml':
            return yaml.dump({key: value}, default_flow_style=False)
        else:
            return f"{key}: {value}"
    
    def _parse_toml_sections(self, content: str) -> List[Dict]:
        """Parse TOML sections using regex."""
        sections = []
        lines = content.split('\n')
        current_section = None
        current_content = []
        
        for line_num, line in enumerate(lines, 1):
            stripped = line.strip()
            
            # Check for section header [section.name]
            section_match = re.match(r'^\[([^\]]+)\]', stripped)
            if section_match:
                # Save previous section
                if current_section:
                    current_section['content'] = '\n'.join(current_content)
                    current_section['end_line'] = line_num - 1
                    sections.append(current_section)
                
                # Start new section
                section_name = section_match.group(1)
                current_section = {
                    'name': section_name,
                    'start_line': line_num,
                    'end_line': line_num,
                    'content': ''
                }
                current_content = [line]
            else:
                if current_section is None:
                    # Content before first section
                    current_section = {
                        'name': 'global',
                        'start_line': 1,
                        'end_line': 1,
                        'content': ''
                    }
                    current_content = []
                
                current_content.append(line)
        
        # Save final section
        if current_section:
            current_section['content'] = '\n'.join(current_content)
            current_section['end_line'] = len(lines)
            sections.append(current_section)
        
        return sections
    
    def _parse_ini_sections(self, content: str) -> List[Dict]:
        """Parse INI sections."""
        sections = []
        lines = content.split('\n')
        current_section = None
        current_content = []
        
        for line_num, line in enumerate(lines, 1):
            stripped = line.strip()
            
            # Skip comments and empty lines for section detection
            if not stripped or stripped.startswith(('#', ';')):
                if current_section:
                    current_content.append(line)
                continue
            
            # Check for section header [section]
            section_match = re.match(r'^\[([^\]]+)\]', stripped)
            if section_match:
                # Save previous section
                if current_section:
                    current_section['content'] = '\n'.join(current_content)
                    current_section['end_line'] = line_num - 1
                    sections.append(current_section)
                
                # Start new section
                section_name = section_match.group(1)
                current_section = {
                    'name': section_name,
                    'start_line': line_num,
                    'end_line': line_num,
                    'content': ''
                }
                current_content = [line]
            else:
                if current_section is None:
                    # Content before first section
                    current_section = {
                        'name': 'global',
                        'start_line': 1,
                        'end_line': 1,
                        'content': ''
                    }
                    current_content = []
                
                current_content.append(line)
        
        # Save final section
        if current_section:
            current_section['content'] = '\n'.join(current_content)
            current_section['end_line'] = len(lines)
            sections.append(current_section)
        
        return sections
    
    def _create_section_chunks(self, sections: List[Dict], file_metadata: FileMetadata, format_type: str) -> List[Chunk]:
        """Create chunks from parsed sections."""
        chunks = []
        
        for i, section in enumerate(sections):
            context = ChunkContext(
                section_headers=[section['name']],
                namespace=section['name']
            )
            
            chunk = self.create_chunk(
                content=section['content'],
                file_metadata=file_metadata,
                start_line=section['start_line'],
                end_line=section['end_line'],
                chunk_type=ChunkType.CONFIG_SECTION,
                chunk_index=i,
                context=context
            )
            
            chunks.append(chunk)
        
        return chunks
    
    def _group_properties_by_prefix(self, content: str) -> Dict[str, List[str]]:
        """Group properties by common prefixes."""
        lines = content.split('\n')
        groups = {}
        
        for line in lines:
            stripped = line.strip()
            if not stripped or stripped.startswith('#'):
                continue
            
            if '=' in stripped:
                key = stripped.split('=')[0].strip()
                # Group by prefix (before first dot or underscore)
                prefix = key.split('.')[0].split('_')[0]
                
                if prefix not in groups:
                    groups[prefix] = []
                groups[prefix].append(line)
        
        return groups
    
    def _create_property_chunks(self, groups: Dict[str, List[str]], file_metadata: FileMetadata) -> List[Chunk]:
        """Create chunks from property groups."""
        chunks = []
        current_line = 1
        
        for i, (prefix, lines) in enumerate(groups.items()):
            content = '\n'.join(lines)
            line_count = len(lines)
            
            context = ChunkContext(
                section_headers=[prefix],
                namespace=prefix
            )
            
            chunk = self.create_chunk(
                content=content,
                file_metadata=file_metadata,
                start_line=current_line,
                end_line=current_line + line_count - 1,
                chunk_type=ChunkType.CONFIG_KEY_VALUE,
                chunk_index=i,
                context=context
            )
            
            chunks.append(chunk)
            current_line += line_count
        
        return chunks
    
    def _chunk_generic(self, content: str, file_metadata: FileMetadata) -> List[Chunk]:
        """Generic chunking for unknown config formats."""
        # Split by logical groups (empty lines)
        sections = content.split('\n\n')
        chunks = []
        current_line = 1
        
        for i, section in enumerate(sections):
            if section.strip():
                line_count = section.count('\n') + 1
                
                chunk = self.create_chunk(
                    content=section.strip(),
                    file_metadata=file_metadata,
                    start_line=current_line,
                    end_line=current_line + line_count - 1,
                    chunk_type=ChunkType.CONFIG_SECTION,
                    chunk_index=i,
                    context=ChunkContext()
                )
                
                chunks.append(chunk)
                current_line += line_count + 1  # +1 for empty line
        
        return chunks
    
    def estimate_chunks(self, content: str) -> int:
        """Estimate number of chunks for config content."""
        # Count sections/top-level keys
        section_count = 0
        
        # Count INI/TOML sections
        section_count += len(re.findall(r'^\[([^\]]+)\]', content, re.MULTILINE))
        
        # Count JSON/YAML top-level keys (approximate)
        brace_count = content.count('{')
        if brace_count > 0:
            section_count = max(section_count, content.count('":'))
        
        # Fallback to line-based estimation
        if section_count == 0:
            lines = [line for line in content.split('\n') if line.strip() and not line.strip().startswith('#')]
            section_count = max(1, len(lines) // 10)  # Rough estimate
        
        return max(1, section_count)
