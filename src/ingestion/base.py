"""
Abstract base classes for the ingestion module.

This module defines the interfaces and abstract base classes that implement
the SOLID principles and provide a foundation for the ingestion pipeline.
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Iterator, Union
from pathlib import Path
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum


@dataclass
class FileMetadata:
    """Metadata for a file in the repository."""
    
    file_path: str
    file_size: int
    file_type: str
    last_modified: datetime
    commit_sha: Optional[str] = None
    branch: Optional[str] = None
    repository_url: Optional[str] = None
    start_line: Optional[int] = None
    end_line: Optional[int] = None
    language: Optional[str] = None
    priority: float = 0.0
    summary: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert metadata to dictionary format."""
        return {
            "file_path": self.file_path,
            "file_size": self.file_size,
            "file_type": self.file_type,
            "last_modified": self.last_modified.isoformat(),
            "commit_sha": self.commit_sha,
            "branch": self.branch,
            "repository_url": self.repository_url,
            "start_line": self.start_line,
            "end_line": self.end_line,
            "language": self.language,
            "priority": self.priority,
            "summary": self.summary,
        }


@dataclass
class RepositoryInfo:
    """Information about a repository."""

    url: str
    name: str
    owner: str
    branch: str
    commit_sha: str
    is_private: bool
    description: Optional[str] = None
    language: Optional[str] = None
    size: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


class ChunkType(Enum):
    """Types of chunks based on content structure."""

    CODE_FUNCTION = "code_function"
    CODE_CLASS = "code_class"
    CODE_MODULE = "code_module"
    MARKDOWN_SECTION = "markdown_section"
    MARKDOWN_CODE_BLOCK = "markdown_code_block"
    TEXT_PARAGRAPH = "text_paragraph"
    CONFIG_SECTION = "config_section"
    CONFIG_KEY_VALUE = "config_key_value"
    GENERIC = "generic"


@dataclass
class ChunkContext:
    """Hierarchical context information for a chunk."""

    parent_chunk_id: Optional[str] = None
    section_headers: List[str] = field(default_factory=list)
    function_name: Optional[str] = None
    class_name: Optional[str] = None
    module_name: Optional[str] = None
    namespace: Optional[str] = None


@dataclass
class Chunk:
    """A chunk of content with metadata and context."""

    # Core content
    content: str
    chunk_id: str
    chunk_type: ChunkType

    # Source information
    file_metadata: FileMetadata
    start_line: int
    end_line: int
    start_char: Optional[int] = None
    end_char: Optional[int] = None

    # Hierarchical context
    context: ChunkContext = field(default_factory=ChunkContext)

    # Metrics
    token_count: Optional[int] = None
    char_count: Optional[int] = None

    # Processing metadata
    chunking_strategy: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)

    # Relationships
    child_chunks: List[str] = field(default_factory=list)
    related_chunks: List[str] = field(default_factory=list)

    def to_dict(self) -> Dict[str, Any]:
        """Convert chunk to dictionary format."""
        return {
            "chunk_id": self.chunk_id,
            "content": self.content,
            "chunk_type": self.chunk_type.value,
            "file_metadata": self.file_metadata.to_dict(),
            "start_line": self.start_line,
            "end_line": self.end_line,
            "start_char": self.start_char,
            "end_char": self.end_char,
            "context": {
                "parent_chunk_id": self.context.parent_chunk_id,
                "section_headers": self.context.section_headers,
                "function_name": self.context.function_name,
                "class_name": self.context.class_name,
                "module_name": self.context.module_name,
                "namespace": self.context.namespace,
            },
            "token_count": self.token_count,
            "char_count": self.char_count,
            "chunking_strategy": self.chunking_strategy,
            "created_at": self.created_at.isoformat(),
            "child_chunks": self.child_chunks,
            "related_chunks": self.related_chunks,
        }

    @property
    def line_range(self) -> str:
        """Get human-readable line range."""
        if self.start_line == self.end_line:
            return f"L{self.start_line}"
        return f"L{self.start_line}-{self.end_line}"

    @property
    def full_context_path(self) -> str:
        """Get full context path for the chunk."""
        parts = []

        if self.context.module_name:
            parts.append(self.context.module_name)

        if self.context.class_name:
            parts.append(self.context.class_name)

        if self.context.function_name:
            parts.append(self.context.function_name)

        if parts:
            return "::".join(parts)

        # Fallback to section headers for markdown
        if self.context.section_headers:
            return " > ".join(self.context.section_headers)

        return self.file_metadata.file_path


@dataclass
class EmbeddingMetadata:
    """Metadata for embeddings."""

    embedding_model: str
    embedding_dimension: int
    embedding_provider: str
    created_at: datetime = field(default_factory=datetime.now)
    model_version: Optional[str] = None
    normalization_applied: bool = False

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        return {
            "embedding_model": self.embedding_model,
            "embedding_dimension": self.embedding_dimension,
            "embedding_provider": self.embedding_provider,
            "created_at": self.created_at.isoformat(),
            "model_version": self.model_version,
            "normalization_applied": self.normalization_applied,
        }


@dataclass
class EmbeddedChunk:
    """A chunk with its embedding vector."""

    chunk: Chunk
    embedding: List[float]
    embedding_metadata: EmbeddingMetadata

    # Search and retrieval metadata
    similarity_score: Optional[float] = None
    retrieval_rank: Optional[int] = None

    @property
    def embedding_id(self) -> str:
        """Get unique embedding ID based on chunk ID."""
        return f"emb_{self.chunk.chunk_id}"

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format for storage."""
        return {
            "id": self.embedding_id,
            "chunk_id": self.chunk.chunk_id,
            "content": self.chunk.content,
            "embedding": self.embedding,
            "metadata": {
                # Chunk metadata
                "file_path": self.chunk.file_metadata.file_path,
                "chunk_type": self.chunk.chunk_type.value,
                "start_line": self.chunk.start_line,
                "end_line": self.chunk.end_line,
                "token_count": self.chunk.token_count,
                "chunking_strategy": self.chunk.chunking_strategy,
                "context": {
                    "full_context_path": self.chunk.full_context_path,
                    "function_name": self.chunk.context.function_name,
                    "class_name": self.chunk.context.class_name,
                    "module_name": self.chunk.context.module_name,
                    "section_headers": self.chunk.context.section_headers,
                },
                # File metadata
                "language": self.chunk.file_metadata.language,
                "priority": self.chunk.file_metadata.priority,
                "file_size": self.chunk.file_metadata.file_size,
                # Embedding metadata
                "embedding_model": self.embedding_metadata.embedding_model,
                "embedding_provider": self.embedding_metadata.embedding_provider,
                "embedding_dimension": self.embedding_metadata.embedding_dimension,
                "created_at": self.embedding_metadata.created_at.isoformat(),
            }
        }


@dataclass
class SearchResult:
    """Result from vector similarity search."""

    embedded_chunk: EmbeddedChunk
    similarity_score: float
    rank: int

    @property
    def chunk(self) -> Chunk:
        """Get the underlying chunk."""
        return self.embedded_chunk.chunk

    @property
    def content(self) -> str:
        """Get the chunk content."""
        return self.embedded_chunk.chunk.content

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary format."""
        result = self.embedded_chunk.to_dict()
        result["similarity_score"] = self.similarity_score
        result["rank"] = self.rank
        return result


class RepositorySource(ABC):
    """Abstract base class for repository sources (GitHub, GitLab, etc.)."""
    
    @abstractmethod
    async def authenticate(self) -> bool:
        """Authenticate with the repository service."""
        pass
    
    @abstractmethod
    async def get_repository_info(self, repository_url: str) -> RepositoryInfo:
        """Get information about a repository."""
        pass
    
    @abstractmethod
    async def list_files(
        self, 
        repository_url: str, 
        branch: str = "main"
    ) -> List[str]:
        """List all files in the repository."""
        pass
    
    @abstractmethod
    async def get_file_content(
        self, 
        repository_url: str, 
        file_path: str, 
        branch: str = "main"
    ) -> str:
        """Get the content of a specific file."""
        pass
    
    @abstractmethod
    async def clone_repository(
        self, 
        repository_url: str, 
        local_path: Path, 
        branch: str = "main"
    ) -> Path:
        """Clone repository to local path."""
        pass


class FileFilter(ABC):
    """Abstract base class for file filtering."""
    
    @abstractmethod
    def should_include_file(
        self, 
        file_path: str, 
        file_size: int,
        file_metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """Determine if a file should be included in processing."""
        pass
    
    @abstractmethod
    def filter_files(self, file_paths: List[str]) -> List[str]:
        """Filter a list of file paths."""
        pass
    
    @abstractmethod
    def get_file_priority(self, file_path: str) -> float:
        """Get priority score for a file (higher = more important)."""
        pass


class MetadataExtractor(ABC):
    """Abstract base class for metadata extraction."""
    
    @abstractmethod
    async def extract_file_metadata(
        self, 
        file_path: str, 
        repository_path: Optional[Path] = None
    ) -> FileMetadata:
        """Extract metadata from a file."""
        pass
    
    @abstractmethod
    async def extract_repository_metadata(
        self, 
        repository_path: Path
    ) -> Dict[str, Any]:
        """Extract metadata from a repository."""
        pass
    
    @abstractmethod
    async def get_commit_info(
        self, 
        repository_path: Path, 
        file_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get commit information for a file or repository."""
        pass


class FileLoader(ABC):
    """Abstract base class for file content loading."""

    @abstractmethod
    async def load_file(self, file_path: Path) -> str:
        """Load file content with proper encoding detection."""
        pass

    @abstractmethod
    def detect_encoding(self, file_path: Path) -> str:
        """Detect file encoding."""
        pass

    @abstractmethod
    def is_supported(self, file_path: Path) -> bool:
        """Check if file type is supported."""
        pass


class ChunkingStrategy(ABC):
    """Abstract base class for chunking strategies."""

    @abstractmethod
    async def chunk_content(
        self,
        content: str,
        file_metadata: FileMetadata,
        **kwargs
    ) -> List[Chunk]:
        """Chunk content into semantic units."""
        pass

    @abstractmethod
    def get_strategy_name(self) -> str:
        """Get the name of this chunking strategy."""
        pass

    @abstractmethod
    def is_applicable(self, file_metadata: FileMetadata) -> bool:
        """Check if this strategy is applicable to the file."""
        pass

    @abstractmethod
    def estimate_chunks(self, content: str) -> int:
        """Estimate number of chunks for the content."""
        pass


class ChunkingPipeline(ABC):
    """Abstract base class for chunking pipelines."""

    @abstractmethod
    async def process_files(
        self,
        file_metadata_list: List[FileMetadata],
        repository_path: Path
    ) -> List[Chunk]:
        """Process files and generate chunks."""
        pass

    @abstractmethod
    async def process_single_file(
        self,
        file_metadata: FileMetadata,
        repository_path: Path
    ) -> List[Chunk]:
        """Process a single file and generate chunks."""
        pass

    @abstractmethod
    def get_chunking_strategy(self, file_metadata: FileMetadata) -> ChunkingStrategy:
        """Get appropriate chunking strategy for a file."""
        pass


class IngestionPipeline(ABC):
    """Abstract base class for ingestion pipelines."""

    @abstractmethod
    async def ingest_repository(
        self,
        repository_url: str,
        branch: str = "main"
    ) -> Dict[str, Any]:
        """Ingest a complete repository."""
        pass

    @abstractmethod
    async def ingest_files(
        self,
        file_paths: List[str],
        repository_path: Path
    ) -> List[FileMetadata]:
        """Ingest specific files from a repository."""
        pass

    @abstractmethod
    async def get_ingestion_status(self, repository_url: str) -> Dict[str, Any]:
        """Get the status of repository ingestion."""
        pass


class EmbeddingClient(ABC):
    """Abstract base class for embedding generation clients."""

    @abstractmethod
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for a list of texts."""
        pass

    @abstractmethod
    def get_embedding_dimension(self) -> int:
        """Get the dimension of embeddings produced by this client."""
        pass

    @abstractmethod
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the embedding model."""
        pass

    @abstractmethod
    async def generate_single_embedding(self, text: str) -> List[float]:
        """Generate embedding for a single text."""
        pass

    @abstractmethod
    def estimate_cost(self, texts: List[str]) -> float:
        """Estimate the cost of generating embeddings for the given texts."""
        pass


class VectorStore(ABC):
    """Abstract base class for vector storage and retrieval."""

    @abstractmethod
    async def add_embeddings(
        self,
        embeddings: List[List[float]],
        metadata: List[Dict[str, Any]],
        ids: List[str]
    ) -> bool:
        """Add embeddings with metadata to the store."""
        pass

    @abstractmethod
    async def search_similar(
        self,
        query_embedding: List[float],
        top_k: int = 10,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Search for similar embeddings."""
        pass

    @abstractmethod
    async def delete_embeddings(self, ids: List[str]) -> bool:
        """Delete embeddings by their IDs."""
        pass

    @abstractmethod
    async def get_embedding(self, embedding_id: str) -> Optional[EmbeddedChunk]:
        """Get a specific embedding by ID."""
        pass

    @abstractmethod
    async def get_collection_info(self) -> Dict[str, Any]:
        """Get information about the collection/index."""
        pass

    @abstractmethod
    async def create_collection(self, collection_name: str) -> bool:
        """Create a new collection/index."""
        pass

    @abstractmethod
    async def delete_collection(self, collection_name: str) -> bool:
        """Delete a collection/index."""
        pass


class EmbeddingPipeline(ABC):
    """Abstract base class for embedding pipelines."""

    @abstractmethod
    async def process_chunks(
        self,
        chunks: List[Chunk]
    ) -> List[EmbeddedChunk]:
        """Process chunks and generate embeddings."""
        pass

    @abstractmethod
    async def store_embeddings(
        self,
        embedded_chunks: List[EmbeddedChunk]
    ) -> bool:
        """Store embeddings in the vector store."""
        pass

    @abstractmethod
    async def search_similar_chunks(
        self,
        query: str,
        top_k: int = 10,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Search for chunks similar to the query."""
        pass

    @abstractmethod
    async def get_pipeline_stats(self) -> Dict[str, Any]:
        """Get processing statistics for the pipeline."""
        pass
