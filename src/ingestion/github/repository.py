"""
GitHub Repository Manager

This module handles repository cloning, local file operations, and Git-based
metadata extraction using GitPython.
"""

import logging
import shutil
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime
import git
from git import Repo, InvalidGitRepositoryError, GitCommandError

from ..exceptions import (
    RepositoryError,
    FileProcessingError,
    ConfigurationError,
)
from ...config import Settings

logger = logging.getLogger(__name__)


class GitHubRepositoryManager:
    """Manages GitHub repository cloning and local operations."""
    
    def __init__(self, settings: Settings):
        """Initialize repository manager with configuration."""
        self.settings = settings
        self.temp_dir = Path(settings.temp_dir)
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
    def _get_repository_local_path(self, repository_url: str) -> Path:
        """Get local path for a repository."""
        # Extract repo name from URL for directory naming
        repo_name = repository_url.split("/")[-1].replace(".git", "")
        if not repo_name:
            raise RepositoryError(
                f"Could not extract repository name from URL: {repository_url}",
                repository_url=repository_url,
                operation="get_local_path"
            )
        
        return self.temp_dir / repo_name
    
    async def clone_repository(
        self, 
        repository_url: str, 
        branch: str = "main",
        force_refresh: bool = False
    ) -> Path:
        """Clone or update a repository locally."""
        local_path = self._get_repository_local_path(repository_url)
        
        try:
            # If repository already exists locally
            if local_path.exists() and not force_refresh:
                logger.info(f"Repository already exists at {local_path}, updating...")
                return await self._update_repository(local_path, branch)
            
            # Remove existing directory if force refresh
            if local_path.exists() and force_refresh:
                logger.info(f"Force refresh: removing existing repository at {local_path}")
                shutil.rmtree(local_path)
            
            # Clone repository
            logger.info(f"Cloning repository {repository_url} to {local_path}")
            
            # Prepare clone URL with authentication if available
            clone_url = self._prepare_clone_url(repository_url)
            
            # Clone with specific branch
            repo = Repo.clone_from(
                clone_url,
                local_path,
                branch=branch,
                depth=1,  # Shallow clone for efficiency
                single_branch=True
            )
            
            logger.info(f"Successfully cloned repository to {local_path}")
            return local_path
            
        except GitCommandError as e:
            logger.error(f"Git command failed while cloning {repository_url}: {e}")
            
            # Try with default branch if main doesn't exist
            if branch == "main" and "does not exist" in str(e):
                logger.info("Branch 'main' not found, trying default branch")
                try:
                    # Clone without specifying branch to get default
                    repo = Repo.clone_from(clone_url, local_path, depth=1)
                    logger.info(f"Successfully cloned repository with default branch to {local_path}")
                    return local_path
                except GitCommandError as e2:
                    raise RepositoryError(
                        f"Failed to clone repository with default branch: {e2}",
                        repository_url=repository_url,
                        operation="clone",
                        details={"error": str(e2), "branch": "default"},
                        cause=e2
                    )
            else:
                raise RepositoryError(
                    f"Failed to clone repository: {e}",
                    repository_url=repository_url,
                    operation="clone",
                    details={"error": str(e), "branch": branch},
                    cause=e
                )
        except Exception as e:
            logger.error(f"Unexpected error while cloning {repository_url}: {e}")
            raise RepositoryError(
                f"Unexpected error during repository cloning: {e}",
                repository_url=repository_url,
                operation="clone",
                cause=e
            )
    
    def _prepare_clone_url(self, repository_url: str) -> str:
        """Prepare clone URL with authentication if available."""
        if self.settings.github_token and repository_url.startswith("https://github.com/"):
            # Insert token into HTTPS URL
            return repository_url.replace(
                "https://github.com/",
                f"https://{self.settings.github_token}@github.com/"
            )
        return repository_url
    
    async def _update_repository(self, local_path: Path, branch: str) -> Path:
        """Update an existing local repository."""
        try:
            repo = Repo(local_path)
            
            # Fetch latest changes
            logger.info(f"Fetching latest changes for repository at {local_path}")
            repo.remotes.origin.fetch()
            
            # Checkout and pull the specified branch
            try:
                repo.git.checkout(branch)
                repo.remotes.origin.pull()
                logger.info(f"Successfully updated repository to latest {branch}")
            except GitCommandError as e:
                if "does not exist" in str(e):
                    # Try default branch
                    default_branch = repo.active_branch.name
                    logger.info(f"Branch {branch} not found, using {default_branch}")
                    repo.git.checkout(default_branch)
                    repo.remotes.origin.pull()
                else:
                    raise
            
            return local_path
            
        except InvalidGitRepositoryError:
            logger.warning(f"Invalid git repository at {local_path}, re-cloning...")
            shutil.rmtree(local_path)
            # This will trigger a fresh clone
            raise RepositoryError(
                "Invalid git repository, needs re-cloning",
                operation="update"
            )
        except GitCommandError as e:
            logger.error(f"Failed to update repository at {local_path}: {e}")
            raise RepositoryError(
                f"Failed to update repository: {e}",
                operation="update",
                details={"error": str(e), "path": str(local_path)},
                cause=e
            )
    
    async def get_file_list(self, repository_path: Path) -> List[str]:
        """Get list of all files in the repository."""
        try:
            if not repository_path.exists():
                raise FileProcessingError(
                    f"Repository path does not exist: {repository_path}",
                    operation="list_files"
                )
            
            files = []
            for file_path in repository_path.rglob("*"):
                if file_path.is_file():
                    # Get relative path from repository root
                    relative_path = file_path.relative_to(repository_path)
                    files.append(str(relative_path))
            
            logger.info(f"Found {len(files)} files in {repository_path}")
            return files
            
        except Exception as e:
            logger.error(f"Failed to list files in {repository_path}: {e}")
            raise FileProcessingError(
                f"Failed to list repository files: {e}",
                operation="list_files",
                details={"path": str(repository_path)},
                cause=e
            )
    
    async def get_file_content(self, repository_path: Path, file_path: str) -> str:
        """Get content of a specific file."""
        try:
            full_path = repository_path / file_path
            
            if not full_path.exists():
                raise FileProcessingError(
                    f"File does not exist: {file_path}",
                    file_path=file_path,
                    operation="read_content"
                )
            
            if not full_path.is_file():
                raise FileProcessingError(
                    f"Path is not a file: {file_path}",
                    file_path=file_path,
                    operation="read_content"
                )
            
            # Read file content with encoding detection
            try:
                with open(full_path, 'r', encoding='utf-8') as f:
                    return f.read()
            except UnicodeDecodeError:
                # Try with latin-1 encoding as fallback
                with open(full_path, 'r', encoding='latin-1') as f:
                    return f.read()
                    
        except Exception as e:
            logger.error(f"Failed to read file {file_path}: {e}")
            raise FileProcessingError(
                f"Failed to read file content: {e}",
                file_path=file_path,
                operation="read_content",
                cause=e
            )
    
    async def get_commit_info(self, repository_path: Path, file_path: Optional[str] = None) -> Dict[str, Any]:
        """Get commit information for repository or specific file."""
        try:
            repo = Repo(repository_path)
            
            if file_path:
                # Get commits for specific file
                commits = list(repo.iter_commits(paths=file_path, max_count=1))
                if not commits:
                    return {
                        "commit_sha": None,
                        "author": None,
                        "date": None,
                        "message": None,
                    }
                commit = commits[0]
            else:
                # Get latest commit for repository
                commit = repo.head.commit
            
            return {
                "commit_sha": commit.hexsha,
                "author": commit.author.name,
                "author_email": commit.author.email,
                "date": datetime.fromtimestamp(commit.committed_date),
                "message": commit.message.strip(),
                "branch": repo.active_branch.name,
            }
            
        except Exception as e:
            logger.error(f"Failed to get commit info: {e}")
            raise RepositoryError(
                f"Failed to get commit information: {e}",
                operation="get_commit_info",
                details={"path": str(repository_path), "file": file_path},
                cause=e
            )
    
    async def cleanup(self, repository_path: Optional[Path] = None) -> None:
        """Clean up temporary repository files."""
        try:
            if repository_path and repository_path.exists():
                logger.info(f"Cleaning up repository at {repository_path}")
                shutil.rmtree(repository_path)
            elif not repository_path:
                # Clean up all temporary repositories
                logger.info(f"Cleaning up all temporary repositories in {self.temp_dir}")
                for item in self.temp_dir.iterdir():
                    if item.is_dir():
                        shutil.rmtree(item)
                        
        except Exception as e:
            logger.warning(f"Failed to cleanup repository files: {e}")
            # Don't raise exception for cleanup failures
