"""
GitHub Metadata Extractor

This module extracts metadata from GitHub repositories and files,
including commit information, file lineage, and repository structure.
"""

import logging
import mimetypes
from typing import Dict, Any, Optional, List
from pathlib import Path
from datetime import datetime
import git
from git import Repo, InvalidGitRepositoryError

from ..base import MetadataExtractor, FileMetadata
from ..exceptions import MetadataExtractionError, RepositoryError
from ...config import Settings

logger = logging.getLogger(__name__)


class GitHubMetadataExtractor(MetadataExtractor):
    """Extracts metadata from GitHub repositories and files."""
    
    # Language detection based on file extensions
    LANGUAGE_MAP = {
        ".py": "python",
        ".js": "javascript", 
        ".ts": "typescript",
        ".jsx": "javascript",
        ".tsx": "typescript",
        ".java": "java",
        ".cpp": "cpp",
        ".c": "c",
        ".h": "c",
        ".hpp": "cpp",
        ".cs": "csharp",
        ".go": "go",
        ".rs": "rust",
        ".php": "php",
        ".rb": "ruby",
        ".swift": "swift",
        ".kt": "kotlin",
        ".scala": "scala",
        ".r": "r",
        ".sql": "sql",
        ".sh": "bash",
        ".bash": "bash",
        ".zsh": "zsh",
        ".fish": "fish",
        ".ps1": "powershell",
        ".bat": "batch",
        ".cmd": "batch",
        ".html": "html",
        ".htm": "html",
        ".css": "css",
        ".scss": "scss",
        ".sass": "sass",
        ".less": "less",
        ".xml": "xml",
        ".json": "json",
        ".yaml": "yaml",
        ".yml": "yaml",
        ".toml": "toml",
        ".ini": "ini",
        ".cfg": "ini",
        ".conf": "config",
        ".md": "markdown",
        ".rst": "restructuredtext",
        ".txt": "text",
        ".dockerfile": "dockerfile",
        ".makefile": "makefile",
        ".cmake": "cmake",
        ".gradle": "gradle",
        ".maven": "maven",
        ".npm": "npm",
        ".lock": "lockfile",
    }
    
    def __init__(self, settings: Settings):
        """Initialize metadata extractor with configuration."""
        self.settings = settings
        
    async def extract_file_metadata(
        self, 
        file_path: str, 
        repository_path: Optional[Path] = None
    ) -> FileMetadata:
        """Extract metadata from a file."""
        try:
            if repository_path:
                full_path = repository_path / file_path
            else:
                full_path = Path(file_path)
            
            if not full_path.exists():
                raise MetadataExtractionError(
                    f"File does not exist: {file_path}",
                    source=file_path,
                    metadata_type="file"
                )
            
            # Basic file information
            stat = full_path.stat()
            file_size = stat.st_size
            last_modified = datetime.fromtimestamp(stat.st_mtime)
            
            # Detect file type and language
            file_extension = full_path.suffix.lower()
            file_type = self._detect_file_type(full_path)
            language = self.LANGUAGE_MAP.get(file_extension)
            
            # Extract Git metadata if in a repository
            commit_info = {}
            if repository_path:
                commit_info = await self._extract_git_metadata(repository_path, file_path)
            
            # Generate summary for certain file types
            summary = await self._generate_file_summary(full_path, file_type)
            
            return FileMetadata(
                file_path=file_path,
                file_size=file_size,
                file_type=file_type,
                last_modified=last_modified,
                commit_sha=commit_info.get("commit_sha"),
                branch=commit_info.get("branch"),
                repository_url=commit_info.get("repository_url"),
                language=language,
                summary=summary,
                priority=0.0,  # Will be set by file filter
            )
            
        except Exception as e:
            logger.error(f"Failed to extract metadata for {file_path}: {e}")
            raise MetadataExtractionError(
                f"Failed to extract file metadata: {e}",
                source=file_path,
                metadata_type="file",
                cause=e
            )
    
    def _detect_file_type(self, file_path: Path) -> str:
        """Detect file type based on extension and content."""
        extension = file_path.suffix.lower()
        
        # Check MIME type
        mime_type, _ = mimetypes.guess_type(str(file_path))
        
        if mime_type:
            if mime_type.startswith("text/"):
                return "text"
            elif mime_type.startswith("application/"):
                if "json" in mime_type:
                    return "json"
                elif "xml" in mime_type:
                    return "xml"
                else:
                    return "binary"
            elif mime_type.startswith("image/"):
                return "image"
            else:
                return "binary"
        
        # Fallback to extension-based detection
        text_extensions = {
            ".py", ".js", ".ts", ".java", ".cpp", ".c", ".h", ".cs", ".go", 
            ".rs", ".php", ".rb", ".swift", ".kt", ".scala", ".r", ".sql",
            ".sh", ".bash", ".zsh", ".fish", ".ps1", ".bat", ".cmd",
            ".html", ".htm", ".css", ".scss", ".sass", ".less", ".xml",
            ".json", ".yaml", ".yml", ".toml", ".ini", ".cfg", ".conf",
            ".md", ".rst", ".txt", ".log", ".dockerfile", ".makefile",
            ".cmake", ".gradle", ".lock"
        }
        
        if extension in text_extensions:
            return "text"
        elif extension in {".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg", ".ico"}:
            return "image"
        elif extension in {".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx"}:
            return "document"
        else:
            return "binary"
    
    async def _extract_git_metadata(
        self, 
        repository_path: Path, 
        file_path: str
    ) -> Dict[str, Any]:
        """Extract Git metadata for a file."""
        try:
            repo = Repo(repository_path)
            
            # Get commits for the specific file
            commits = list(repo.iter_commits(paths=file_path, max_count=1))
            
            if commits:
                commit = commits[0]
                return {
                    "commit_sha": commit.hexsha,
                    "branch": repo.active_branch.name,
                    "author": commit.author.name,
                    "author_email": commit.author.email,
                    "commit_date": datetime.fromtimestamp(commit.committed_date),
                    "commit_message": commit.message.strip(),
                }
            else:
                # File might be new/untracked
                return {
                    "commit_sha": None,
                    "branch": repo.active_branch.name,
                }
                
        except (InvalidGitRepositoryError, Exception) as e:
            logger.warning(f"Failed to extract Git metadata for {file_path}: {e}")
            return {}
    
    async def _generate_file_summary(self, file_path: Path, file_type: str) -> Optional[str]:
        """Generate a brief summary of the file content."""
        try:
            if file_type != "text" or file_path.stat().st_size > 10000:  # Skip large files
                return None
            
            # Read first few lines for summary
            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                lines = []
                for i, line in enumerate(f):
                    if i >= 10:  # Read max 10 lines
                        break
                    line = line.strip()
                    if line and not line.startswith("#") and not line.startswith("//"):
                        lines.append(line)
                
                if lines:
                    # Take first meaningful line as summary
                    summary = lines[0]
                    if len(summary) > 100:
                        summary = summary[:97] + "..."
                    return summary
            
            return None
            
        except Exception as e:
            logger.debug(f"Failed to generate summary for {file_path}: {e}")
            return None
    
    async def extract_repository_metadata(
        self, 
        repository_path: Path
    ) -> Dict[str, Any]:
        """Extract metadata from a repository."""
        try:
            if not repository_path.exists():
                raise MetadataExtractionError(
                    f"Repository path does not exist: {repository_path}",
                    source=str(repository_path),
                    metadata_type="repository"
                )
            
            metadata = {
                "path": str(repository_path),
                "name": repository_path.name,
                "extracted_at": datetime.now().isoformat(),
            }
            
            # Extract Git metadata if it's a Git repository
            try:
                repo = Repo(repository_path)
                
                # Get repository information
                metadata.update({
                    "is_git_repo": True,
                    "current_branch": repo.active_branch.name,
                    "head_commit": repo.head.commit.hexsha,
                    "remote_url": self._get_remote_url(repo),
                    "total_commits": len(list(repo.iter_commits())),
                    "branches": [branch.name for branch in repo.branches],
                    "tags": [tag.name for tag in repo.tags],
                })
                
                # Get latest commit info
                latest_commit = repo.head.commit
                metadata.update({
                    "latest_commit": {
                        "sha": latest_commit.hexsha,
                        "author": latest_commit.author.name,
                        "author_email": latest_commit.author.email,
                        "date": datetime.fromtimestamp(latest_commit.committed_date).isoformat(),
                        "message": latest_commit.message.strip(),
                    }
                })
                
            except InvalidGitRepositoryError:
                metadata["is_git_repo"] = False
            
            # Count files by type
            file_counts = await self._count_files_by_type(repository_path)
            metadata["file_counts"] = file_counts
            
            return metadata
            
        except Exception as e:
            logger.error(f"Failed to extract repository metadata: {e}")
            raise MetadataExtractionError(
                f"Failed to extract repository metadata: {e}",
                source=str(repository_path),
                metadata_type="repository",
                cause=e
            )
    
    def _get_remote_url(self, repo: Repo) -> Optional[str]:
        """Get the remote URL of the repository."""
        try:
            if repo.remotes:
                return repo.remotes.origin.url
        except Exception:
            pass
        return None
    
    async def _count_files_by_type(self, repository_path: Path) -> Dict[str, int]:
        """Count files by type in the repository."""
        counts = {}
        
        try:
            for file_path in repository_path.rglob("*"):
                if file_path.is_file():
                    file_type = self._detect_file_type(file_path)
                    counts[file_type] = counts.get(file_type, 0) + 1
        except Exception as e:
            logger.warning(f"Failed to count files by type: {e}")
        
        return counts
    
    async def get_commit_info(
        self, 
        repository_path: Path, 
        file_path: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get commit information for a file or repository."""
        try:
            repo = Repo(repository_path)
            
            if file_path:
                # Get commits for specific file
                commits = list(repo.iter_commits(paths=file_path, max_count=5))
                return {
                    "file_path": file_path,
                    "commit_count": len(commits),
                    "commits": [
                        {
                            "sha": commit.hexsha,
                            "author": commit.author.name,
                            "date": datetime.fromtimestamp(commit.committed_date).isoformat(),
                            "message": commit.message.strip(),
                        }
                        for commit in commits
                    ]
                }
            else:
                # Get repository commit info
                latest_commit = repo.head.commit
                return {
                    "repository_path": str(repository_path),
                    "current_branch": repo.active_branch.name,
                    "head_commit": latest_commit.hexsha,
                    "latest_commit": {
                        "sha": latest_commit.hexsha,
                        "author": latest_commit.author.name,
                        "date": datetime.fromtimestamp(latest_commit.committed_date).isoformat(),
                        "message": latest_commit.message.strip(),
                    }
                }
                
        except Exception as e:
            logger.error(f"Failed to get commit info: {e}")
            raise MetadataExtractionError(
                f"Failed to get commit information: {e}",
                source=str(repository_path),
                metadata_type="commit",
                cause=e
            )
