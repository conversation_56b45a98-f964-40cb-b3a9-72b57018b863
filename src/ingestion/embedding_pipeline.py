"""
Embedding Pipeline

This module provides the main orchestrator for the embedding pipeline,
coordinating embedding generation, vector storage, and quality validation.
"""

import asyncio
import logging
import hashlib
from typing import List, Dict, Any, Optional
from datetime import datetime

from .base import Embedding<PERSON><PERSON><PERSON><PERSON>, Chunk, Embedded<PERSON>hunk, SearchResult, EmbeddingMetadata
from .embedding import EmbeddingClientFactory
from .vector import VectorStoreFactory
from .exceptions import IngestionError
from ..config import Settings

logger = logging.getLogger(__name__)


class DefaultEmbeddingPipeline(EmbeddingPipeline):
    """Default implementation of the embedding pipeline."""
    
    def __init__(
        self,
        settings: Settings,
        embedding_client=None,
        vector_store=None,
    ):
        """Initialize embedding pipeline with dependencies."""
        self.settings = settings
        
        # Initialize clients
        self.embedding_client = embedding_client or EmbeddingClientFactory.create_client(settings)
        self.vector_store = vector_store or VectorStoreFactory.create_vector_store(settings)
        
        # Configuration
        self.batch_size = settings.embedding_pipeline_batch_size
        self.quality_check = settings.embedding_quality_check
        self.cache_enabled = settings.embedding_cache_enabled
        self.normalize_embeddings = settings.embedding_normalize
        
        # Caching for duplicate content
        self._embedding_cache: Dict[str, List[float]] = {}
        
        # Processing statistics
        self._stats = {
            "chunks_processed": 0,
            "embeddings_generated": 0,
            "embeddings_cached": 0,
            "embeddings_stored": 0,
            "failed_chunks": 0,
            "processing_time": 0.0,
            "cache_hits": 0,
            "quality_failures": 0,
        }
        
        logger.info("Initialized embedding pipeline")
    
    async def process_chunks(self, chunks: List[Chunk]) -> List[EmbeddedChunk]:
        """Process chunks and generate embeddings."""
        start_time = datetime.now()
        
        try:
            logger.info(f"Processing {len(chunks)} chunks for embedding generation")
            
            embedded_chunks = []
            
            # Process chunks in batches
            for i in range(0, len(chunks), self.batch_size):
                batch = chunks[i:i + self.batch_size]
                logger.debug(f"Processing batch {i//self.batch_size + 1}: {len(batch)} chunks")
                
                batch_embedded = await self._process_batch(batch)
                embedded_chunks.extend(batch_embedded)
            
            end_time = datetime.now()
            self._stats["processing_time"] = (end_time - start_time).total_seconds()
            self._stats["chunks_processed"] = len(chunks)
            
            logger.info(
                f"Embedding generation completed: {len(embedded_chunks)} embedded chunks "
                f"from {len(chunks)} input chunks in {self._stats['processing_time']:.2f} seconds"
            )
            
            return embedded_chunks
            
        except Exception as e:
            logger.error(f"Failed to process chunks for embedding: {e}")
            raise IngestionError(
                f"Embedding pipeline failed: {e}",
                details={
                    "chunk_count": len(chunks),
                    "batch_size": self.batch_size,
                },
                cause=e
            )
    
    async def _process_batch(self, chunks: List[Chunk]) -> List[EmbeddedChunk]:
        """Process a batch of chunks."""
        embedded_chunks = []
        
        # Prepare texts and check cache
        texts_to_embed = []
        cached_embeddings = []
        content_to_chunks = {}  # Map content hash to chunk indices
        unique_texts = {}  # Map content hash to text index in texts_to_embed

        for i, chunk in enumerate(chunks):
            content_hash = self._get_content_hash(chunk.content)

            if self.cache_enabled and content_hash in self._embedding_cache:
                # Use cached embedding
                cached_embedding = self._embedding_cache[content_hash]
                cached_embeddings.append((i, cached_embedding))
                self._stats["cache_hits"] += 1
            else:
                # Track which chunks need this content
                if content_hash not in content_to_chunks:
                    content_to_chunks[content_hash] = []
                content_to_chunks[content_hash].append(i)

                # Add unique content to texts_to_embed
                if content_hash not in unique_texts:
                    texts_to_embed.append(chunk.content)
                    unique_texts[content_hash] = len(texts_to_embed) - 1
        
        # Generate embeddings for non-cached content
        new_embeddings = []
        if texts_to_embed:
            try:
                new_embeddings = await self.embedding_client.generate_embeddings(texts_to_embed)
                self._stats["embeddings_generated"] += len(new_embeddings)
                
                # Cache new embeddings and map to chunks
                if self.cache_enabled:
                    for text, embedding in zip(texts_to_embed, new_embeddings):
                        content_hash = self._get_content_hash(text)
                        self._embedding_cache[content_hash] = embedding
                
            except Exception as e:
                logger.error(f"Failed to generate embeddings for batch: {e}")
                self._stats["failed_chunks"] += len(texts_to_embed)
                raise
        
        # Combine cached and new embeddings
        all_embeddings = [None] * len(chunks)
        
        # Add cached embeddings
        for chunk_idx, embedding in cached_embeddings:
            all_embeddings[chunk_idx] = embedding
        
        # Add new embeddings to all chunks with the same content
        for content_hash, chunk_indices in content_to_chunks.items():
            if content_hash in unique_texts:
                text_idx = unique_texts[content_hash]
                if text_idx < len(new_embeddings):
                    embedding = new_embeddings[text_idx]
                    # Assign this embedding to all chunks with this content
                    for chunk_idx in chunk_indices:
                        all_embeddings[chunk_idx] = embedding
        
        # Create EmbeddedChunk objects
        embedding_metadata = EmbeddingMetadata(
            embedding_model=self.embedding_client.get_model_info()["model"],
            embedding_dimension=self.embedding_client.get_embedding_dimension(),
            embedding_provider=self.embedding_client.get_model_info()["provider"],
            normalization_applied=self.normalize_embeddings
        )
        
        for chunk, embedding in zip(chunks, all_embeddings):
            if embedding is None:
                logger.warning(f"No embedding generated for chunk {chunk.chunk_id}")
                self._stats["failed_chunks"] += 1
                continue

            # Quality check
            if self.quality_check and not self._validate_embedding(embedding):
                logger.warning(f"Embedding quality check failed for chunk {chunk.chunk_id}")
                self._stats["quality_failures"] += 1
                self._stats["failed_chunks"] += 1
                continue
            
            # Normalize if requested
            if self.normalize_embeddings:
                embedding = self._normalize_embedding(embedding)
            
            embedded_chunk = EmbeddedChunk(
                chunk=chunk,
                embedding=embedding,
                embedding_metadata=embedding_metadata
            )
            
            embedded_chunks.append(embedded_chunk)
        
        return embedded_chunks
    
    def _get_content_hash(self, content: str) -> str:
        """Get hash of content for caching."""
        return hashlib.md5(content.encode('utf-8')).hexdigest()
    
    def _validate_embedding(self, embedding: List[float]) -> bool:
        """Validate embedding quality."""
        if not embedding:
            return False
        
        # Check dimension
        expected_dim = self.embedding_client.get_embedding_dimension()
        if len(embedding) != expected_dim:
            return False
        
        # Check for NaN or infinite values
        if any(not isinstance(x, (int, float)) or x != x or abs(x) == float('inf') for x in embedding):
            return False
        
        # Check if embedding is all zeros
        if all(x == 0 for x in embedding):
            return False
        
        return True
    
    def _normalize_embedding(self, embedding: List[float]) -> List[float]:
        """Normalize embedding to unit vector."""
        import math
        
        # Calculate magnitude
        magnitude = math.sqrt(sum(x * x for x in embedding))
        
        if magnitude == 0:
            return embedding
        
        # Normalize
        return [x / magnitude for x in embedding]
    
    async def store_embeddings(self, embedded_chunks: List[EmbeddedChunk]) -> bool:
        """Store embeddings in the vector store."""
        if not embedded_chunks:
            return True
        
        try:
            logger.info(f"Storing {len(embedded_chunks)} embeddings in vector store")
            
            # Prepare data for storage
            embeddings = [chunk.embedding for chunk in embedded_chunks]
            metadata = [chunk.to_dict()["metadata"] for chunk in embedded_chunks]
            ids = [chunk.embedding_id for chunk in embedded_chunks]
            
            # Store in vector store
            success = await self.vector_store.add_embeddings(embeddings, metadata, ids)
            
            if success:
                self._stats["embeddings_stored"] += len(embedded_chunks)
                logger.info(f"Successfully stored {len(embedded_chunks)} embeddings")
            
            return success
            
        except Exception as e:
            logger.error(f"Failed to store embeddings: {e}")
            raise IngestionError(
                f"Failed to store embeddings: {e}",
                operation="store_embeddings",
                cause=e
            )
    
    async def search_similar_chunks(
        self, 
        query: str, 
        top_k: int = 10,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[SearchResult]:
        """Search for chunks similar to the query."""
        try:
            # Generate embedding for query
            query_embedding = await self.embedding_client.generate_single_embedding(query)
            
            if not query_embedding:
                raise IngestionError("Failed to generate embedding for query")
            
            # Normalize query embedding if configured
            if self.normalize_embeddings:
                query_embedding = self._normalize_embedding(query_embedding)
            
            # Search in vector store
            results = await self.vector_store.search_similar(
                query_embedding=query_embedding,
                top_k=top_k,
                filters=filters
            )
            
            logger.debug(f"Found {len(results)} similar chunks for query")
            return results
            
        except Exception as e:
            logger.error(f"Failed to search similar chunks: {e}")
            raise IngestionError(
                f"Failed to search similar chunks: {e}",
                operation="search_similar_chunks",
                cause=e
            )
    
    async def get_pipeline_stats(self) -> Dict[str, Any]:
        """Get processing statistics for the pipeline."""
        # Combine pipeline stats with client and store stats
        stats = self._stats.copy()
        
        # Add embedding client stats
        if hasattr(self.embedding_client, 'get_stats'):
            stats["embedding_client"] = self.embedding_client.get_stats()
        
        # Add vector store stats
        if hasattr(self.vector_store, 'get_stats'):
            stats["vector_store"] = self.vector_store.get_stats()
        
        # Add collection info
        try:
            collection_info = await self.vector_store.get_collection_info()
            stats["collection_info"] = collection_info
        except Exception as e:
            logger.debug(f"Could not get collection info: {e}")
        
        return stats
    
    def clear_cache(self) -> None:
        """Clear the embedding cache."""
        self._embedding_cache.clear()
        logger.debug("Cleared embedding cache")
    
    def reset_stats(self) -> None:
        """Reset processing statistics."""
        self._stats = {
            "chunks_processed": 0,
            "embeddings_generated": 0,
            "embeddings_cached": 0,
            "embeddings_stored": 0,
            "failed_chunks": 0,
            "processing_time": 0.0,
            "cache_hits": 0,
            "quality_failures": 0,
        }
