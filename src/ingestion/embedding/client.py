"""
Embedding Client Implementations

This module provides concrete implementations of embedding clients for
different providers, with rate limiting, error handling, and cost optimization.
"""

import asyncio
import logging
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
import openai
from openai import AsyncOpenAI

from ..base import Embedding<PERSON>lient
from ..exceptions import IngestionError
from ...config import Settings

logger = logging.getLogger(__name__)


@dataclass
class RateLimitInfo:
    """Rate limiting information for API calls."""
    
    requests_per_minute: int
    tokens_per_minute: int
    current_requests: int = 0
    current_tokens: int = 0
    window_start: float = 0.0
    
    def reset_if_needed(self) -> None:
        """Reset counters if window has passed."""
        current_time = time.time()
        if current_time - self.window_start >= 60:  # 1 minute window
            self.current_requests = 0
            self.current_tokens = 0
            self.window_start = current_time
    
    def can_make_request(self, estimated_tokens: int) -> bool:
        """Check if request can be made within rate limits."""
        self.reset_if_needed()
        return (
            self.current_requests < self.requests_per_minute and
            self.current_tokens + estimated_tokens <= self.tokens_per_minute
        )
    
    def record_request(self, tokens_used: int) -> None:
        """Record a completed request."""
        self.reset_if_needed()
        self.current_requests += 1
        self.current_tokens += tokens_used


class OpenAIEmbeddingClient(EmbeddingClient):
    """OpenAI embedding client with rate limiting and error handling."""
    
    # Model configurations
    MODEL_CONFIGS = {
        "text-embedding-3-large": {
            "dimension": 3072,
            "max_tokens": 8191,
            "cost_per_1k_tokens": 0.00013,
        },
        "text-embedding-3-small": {
            "dimension": 1536,
            "max_tokens": 8191,
            "cost_per_1k_tokens": 0.00002,
        },
        "text-embedding-ada-002": {
            "dimension": 1536,
            "max_tokens": 8191,
            "cost_per_1k_tokens": 0.0001,
        },
    }
    
    def __init__(self, settings: Settings):
        """Initialize OpenAI embedding client."""
        self.settings = settings
        self.client = AsyncOpenAI(api_key=settings.openai_api_key)
        self.model = settings.embedding_model
        self.batch_size = settings.embedding_batch_size
        self.max_retries = settings.embedding_max_retries
        self.timeout = settings.embedding_timeout
        
        # Validate model
        if self.model not in self.MODEL_CONFIGS:
            raise IngestionError(
                f"Unsupported embedding model: {self.model}",
                details={"supported_models": list(self.MODEL_CONFIGS.keys())}
            )
        
        self.model_config = self.MODEL_CONFIGS[self.model]
        
        # Rate limiting (conservative defaults)
        self.rate_limit = RateLimitInfo(
            requests_per_minute=3000,  # OpenAI default
            tokens_per_minute=1000000,  # OpenAI default
        )
        
        # Statistics
        self._stats = {
            "total_requests": 0,
            "total_tokens": 0,
            "total_cost": 0.0,
            "failed_requests": 0,
            "rate_limit_waits": 0,
        }
        
        logger.info(f"Initialized OpenAI embedding client with model {self.model}")
    
    async def generate_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for a list of texts."""
        if not texts:
            return []
        
        # Process in batches to respect rate limits
        all_embeddings = []
        
        for i in range(0, len(texts), self.batch_size):
            batch = texts[i:i + self.batch_size]
            batch_embeddings = await self._generate_batch_embeddings(batch)
            all_embeddings.extend(batch_embeddings)
        
        logger.debug(f"Generated {len(all_embeddings)} embeddings for {len(texts)} texts")
        return all_embeddings
    
    async def _generate_batch_embeddings(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for a batch of texts."""
        # Estimate tokens for rate limiting
        estimated_tokens = sum(len(text.split()) * 1.3 for text in texts)  # Rough estimate
        
        # Wait for rate limit if needed
        await self._wait_for_rate_limit(estimated_tokens)
        
        for attempt in range(self.max_retries + 1):
            try:
                response = await self.client.embeddings.create(
                    model=self.model,
                    input=texts,
                    timeout=self.timeout
                )
                
                # Extract embeddings
                embeddings = [data.embedding for data in response.data]
                
                # Update statistics
                tokens_used = response.usage.total_tokens
                self._update_stats(tokens_used, len(texts))
                self.rate_limit.record_request(tokens_used)
                
                return embeddings
                
            except openai.RateLimitError as e:
                logger.warning(f"Rate limit hit, attempt {attempt + 1}/{self.max_retries + 1}")
                self._stats["rate_limit_waits"] += 1
                
                if attempt < self.max_retries:
                    wait_time = min(2 ** attempt, 60)  # Exponential backoff, max 60s
                    await asyncio.sleep(wait_time)
                else:
                    self._stats["failed_requests"] += 1
                    raise IngestionError(
                        f"Rate limit exceeded after {self.max_retries} retries",
                        details={"operation": "generate_embeddings", "retries": self.max_retries},
                        cause=e
                    )
            
            except openai.APIError as e:
                logger.error(f"OpenAI API error on attempt {attempt + 1}: {e}")
                
                if attempt < self.max_retries:
                    wait_time = min(2 ** attempt, 30)
                    await asyncio.sleep(wait_time)
                else:
                    self._stats["failed_requests"] += 1
                    raise IngestionError(
                        f"OpenAI API error after {self.max_retries} retries: {e}",
                        details={"operation": "generate_embeddings", "retries": self.max_retries},
                        cause=e
                    )
            
            except Exception as e:
                logger.error(f"Unexpected error generating embeddings: {e}")
                self._stats["failed_requests"] += 1
                raise IngestionError(
                    f"Failed to generate embeddings: {e}",
                    details={"operation": "generate_embeddings"},
                    cause=e
                )
    
    async def _wait_for_rate_limit(self, estimated_tokens: int) -> None:
        """Wait if necessary to respect rate limits."""
        while not self.rate_limit.can_make_request(estimated_tokens):
            logger.debug("Waiting for rate limit window to reset")
            await asyncio.sleep(1)
    
    def _update_stats(self, tokens_used: int, num_texts: int) -> None:
        """Update usage statistics."""
        self._stats["total_requests"] += 1
        self._stats["total_tokens"] += tokens_used
        self._stats["total_cost"] += (tokens_used / 1000) * self.model_config["cost_per_1k_tokens"]
    
    async def generate_single_embedding(self, text: str) -> List[float]:
        """Generate embedding for a single text."""
        embeddings = await self.generate_embeddings([text])
        return embeddings[0] if embeddings else []
    
    def get_embedding_dimension(self) -> int:
        """Get the dimension of embeddings produced by this client."""
        return self.model_config["dimension"]
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the embedding model."""
        return {
            "provider": "openai",
            "model": self.model,
            "dimension": self.model_config["dimension"],
            "max_tokens": self.model_config["max_tokens"],
            "cost_per_1k_tokens": self.model_config["cost_per_1k_tokens"],
        }
    
    def estimate_cost(self, texts: List[str]) -> float:
        """Estimate the cost of generating embeddings for the given texts."""
        estimated_tokens = sum(len(text.split()) * 1.3 for text in texts)
        return (estimated_tokens / 1000) * self.model_config["cost_per_1k_tokens"]
    
    def get_stats(self) -> Dict[str, Any]:
        """Get usage statistics."""
        return self._stats.copy()
    
    def reset_stats(self) -> None:
        """Reset usage statistics."""
        self._stats = {
            "total_requests": 0,
            "total_tokens": 0,
            "total_cost": 0.0,
            "failed_requests": 0,
            "rate_limit_waits": 0,
        }


class EmbeddingClientFactory:
    """Factory for creating embedding clients."""
    
    @staticmethod
    def create_client(settings: Settings) -> EmbeddingClient:
        """Create an embedding client based on settings."""
        provider = settings.embedding_provider.lower()
        
        if provider == "openai":
            return OpenAIEmbeddingClient(settings)
        elif provider == "local":
            from .local import LocalEmbeddingClient
            return LocalEmbeddingClient(settings)
        else:
            raise IngestionError(
                f"Unsupported embedding provider: {provider}",
                details={"supported_providers": ["openai", "local"]}
            )
    
    @staticmethod
    def get_available_providers() -> List[str]:
        """Get list of available embedding providers."""
        return ["openai", "local"]

    @staticmethod
    def validate_provider_config(settings: Settings) -> bool:
        """Validate provider configuration."""
        provider = settings.embedding_provider.lower()

        if provider == "openai":
            if not settings.openai_api_key:
                raise IngestionError("OpenAI API key is required for OpenAI embedding provider")
            return True
        elif provider == "local":
            # Local provider validation can be added here
            return True
        else:
            return False
